<template>
  <div class="md-profile">
    <div class="md-container">
      <h1 class="md-profile__title">My Profile</h1>
      
      <!-- Tabs navigation -->
      <div class="md-tabs">
        <button 
          class="md-tab" 
          :class="{ 'md-tab--active': activeTab === 'profile' }" 
          @click="activeTab = 'profile'"
        >
          <span class="material-icons-round">person</span>
          Personal Info
        </button>
        <button 
          class="md-tab" 
          :class="{ 'md-tab--active': activeTab === 'addresses' }" 
          @click="activeTab = 'addresses'"
        >
          <span class="material-icons-round">location_on</span>
          Addresses
        </button>
        <button 
          class="md-tab" 
          :class="{ 'md-tab--active': activeTab === 'current_orders' }" 
          @click="activeTab = 'current_orders'"
        >
          <span class="material-icons-round">local_shipping</span>
          Current Orders
        </button>
        <button 
          class="md-tab" 
          :class="{ 'md-tab--active': activeTab === 'orders' }" 
          @click="activeTab = 'orders'"
        >
          <span class="material-icons-round">receipt_long</span>
          Order History
        </button>
      </div>
      
      <!-- Profile information section -->
      <div v-if="activeTab === 'profile'" class="md-tab-content">
        <div class="md-profile__section">
          <div class="md-profile__header">
            <h2 class="md-profile__section-title">Personal Information</h2>
            <button v-if="!isEditing" @click="startEditing" class="md-button md-button--outline">
              <span class="material-icons-round">edit</span>
              Edit
            </button>
            <div v-else class="md-button-group">
              <button @click="cancelEditing" class="md-button md-button--text">Cancel</button>
              <button @click="saveProfile" class="md-button md-button--filled">Save</button>
            </div>
          </div>
          
          <div class="md-profile__user-info">
            <div class="md-profile__avatar-container">
              <div class="md-profile__avatar">
                <img :src="profileData.avatar || defaultAvatarUrl" alt="Profile avatar" />
                <div v-if="isEditing" class="md-profile__avatar-edit">
                  <label for="avatar-upload" class="md-profile__avatar-edit-button">
                    <span class="material-icons-round">photo_camera</span>
                  </label>
                  <input type="file" id="avatar-upload" @change="handleAvatarChange" accept="image/*" class="md-profile__avatar-input" />
                </div>
              </div>
            </div>
            
            <div class="md-profile__details">
              <div v-if="!isEditing" class="md-profile__info-group">
                <div class="md-profile__info-item">
                  <span class="material-icons-round md-profile__info-icon">person</span>
                  <div class="md-profile__info-content">
                    <h3 class="md-profile__info-label">Name</h3>
                    <p class="md-profile__info-value">{{ profileData.name }}</p>
                  </div>
                </div>
                
                <div class="md-profile__info-item">
                  <span class="material-icons-round md-profile__info-icon">email</span>
                  <div class="md-profile__info-content">
                    <h3 class="md-profile__info-label">Email</h3>
                    <p class="md-profile__info-value">{{ profileData.email }}</p>
                  </div>
                </div>
                
                <div class="md-profile__info-item">
                  <span class="material-icons-round md-profile__info-icon">phone</span>
                  <div class="md-profile__info-content">
                    <h3 class="md-profile__info-label">Phone</h3>
                    <p class="md-profile__info-value">{{ profileData.phone }}</p>
                  </div>
                </div>
                
                <div class="md-profile__info-item">
                  <span class="material-icons-round md-profile__info-icon">cake</span>
                  <div class="md-profile__info-content">
                    <h3 class="md-profile__info-label">Birthday</h3>
                    <p class="md-profile__info-value">{{ formatDate(profileData.birth) }}</p>
                  </div>
                </div>
              </div>
              
              <!-- Edit form -->
              <div v-else class="md-profile__edit-form">
                <div class="md-textfield">
                  <label for="profile-name" class="md-textfield__label">Name</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">person</span>
                    <input type="text" id="profile-name" v-model="editForm.name" class="md-textfield__input" />
                  </div>
                  <p v-if="formErrors.name" class="md-textfield__error">{{ formErrors.name }}</p>
                </div>
                
                <div class="md-textfield">
                  <label for="profile-email" class="md-textfield__label">Email</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">email</span>
                    <input type="email" id="profile-email" v-model="editForm.email" class="md-textfield__input" />
                  </div>
                  <p v-if="formErrors.email" class="md-textfield__error">{{ formErrors.email }}</p>
                </div>
                
                <div class="md-textfield">
                  <label for="profile-phone" class="md-textfield__label">Phone</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">phone</span>
                    <input type="tel" id="profile-phone" v-model="editForm.phone" class="md-textfield__input" placeholder="+84xxxxxxxxx or 0xxxxxxxxx" />
                  </div>
                  <p v-if="formErrors.phone" class="md-textfield__error">{{ formErrors.phone }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Addresses section -->
      <div v-if="activeTab === 'addresses'" class="md-tab-content">
        <div class="md-profile__section">
          <div class="md-profile__header">
            <h2 class="md-profile__section-title">Delivery Addresses</h2>
            <button v-if="!isAddingAddress" @click="startAddingAddress" class="md-button md-button--outline">
              <span class="material-icons-round">add</span>
              Add Address
            </button>
          </div>
          
          <!-- Address List -->
          <div v-if="addresses.length > 0" class="md-address-list">
            <div v-for="(address, index) in addresses" :key="index" class="md-address-card">
              <div class="md-address-card__content">
                <span v-if="address.isDefault" class="md-address-card__badge">Default</span>
                <h3 class="md-address-card__title">{{ address.label || 'Address ' + (index + 1) }}</h3>
                <p class="md-address-card__text">{{ address.street }}</p>
                <p class="md-address-card__text">{{ address.city }}, {{ address.zipCode }}</p>
              </div>
              <div class="md-address-card__actions">
                <button @click="editAddress(index)" class="md-button md-button--text">
                  <span class="material-icons-round">edit</span>
                  Edit
                </button>
                <button @click="deleteAddress(index)" class="md-button md-button--text">
                  <span class="material-icons-round">delete</span>
                  Delete
                </button>
                <button 
                  v-if="!address.isDefault" 
                  @click="setDefaultAddress(index)" 
                  class="md-button md-button--text"
                >
                  <span class="material-icons-round">star</span>
                  Set as Default
                </button>
              </div>
            </div>
          </div>
          
          <!-- Empty State -->
          <div v-else class="md-empty-state">
            <span class="material-icons-round md-empty-state__icon">location_off</span>
            <h3 class="md-empty-state__title">No saved addresses</h3>
            <p class="md-empty-state__text">Add a delivery address to speed up your checkout process</p>
            <button @click="startAddingAddress" class="md-button md-button--filled">
              <span class="material-icons-round">add</span>
              Add Address
            </button>
          </div>
          
          <!-- Add/Edit Address Form -->
          <div v-if="isAddingAddress || isEditingAddress" class="md-address-form">
            <div class="md-overlay"></div>
            <div class="md-dialog">
              <div class="md-dialog__header">
                <h3 class="md-dialog__title">{{ isEditingAddress ? 'Edit Address' : 'Add New Address' }}</h3>
                <button @click="cancelAddressForm" class="md-dialog__close">
                  <span class="material-icons-round">close</span>
                </button>
              </div>
              <div class="md-dialog__content">
                <div class="md-textfield">
                  <label for="address-label" class="md-textfield__label">Address Label</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">label</span>
                    <select id="address-label" v-model="newAddress.address_label" class="md-textfield__input">
                      <option value="Home">🏠 Home</option>
                      <option value="Work">🏢 Work</option>
                      <option value="Other">📍 Other</option>
                    </select>
                  </div>
                </div>

                <div class="md-textfield">
                  <label for="recipient-name" class="md-textfield__label">Recipient Name *</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">person</span>
                    <input type="text" id="recipient-name" v-model="newAddress.recipient_name" class="md-textfield__input" placeholder="Full name" />
                  </div>
                  <p v-if="addressErrors.recipient_name" class="md-textfield__error">{{ addressErrors.recipient_name }}</p>
                </div>

                <div class="md-textfield">
                  <label for="recipient-phone" class="md-textfield__label">Phone Number *</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">phone</span>
                    <input type="tel" id="recipient-phone" v-model="newAddress.recipient_phone" class="md-textfield__input" placeholder="0123456789" />
                  </div>
                  <p v-if="addressErrors.recipient_phone" class="md-textfield__error">{{ addressErrors.recipient_phone }}</p>
                </div>

                <div class="md-textfield">
                  <label for="address-line1" class="md-textfield__label">Street Address *</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">home</span>
                    <input type="text" id="address-line1" v-model="newAddress.address_line1" class="md-textfield__input" placeholder="House number, street name" />
                  </div>
                  <p v-if="addressErrors.address_line1" class="md-textfield__error">{{ addressErrors.address_line1 }}</p>
                </div>

                <div class="md-textfield">
                  <label for="address-line2" class="md-textfield__label">Ward/Commune</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">location_on</span>
                    <input type="text" id="address-line2" v-model="newAddress.address_line2" class="md-textfield__input" placeholder="Ward, commune" />
                  </div>
                </div>

                <div class="md-textfield">
                  <label for="district" class="md-textfield__label">District</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">map</span>
                    <input type="text" id="district" v-model="newAddress.district" class="md-textfield__input" placeholder="District" />
                  </div>
                </div>

                <div class="md-textfield">
                  <label for="address-city" class="md-textfield__label">City/Province *</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">location_city</span>
                    <input type="text" id="address-city" v-model="newAddress.city" class="md-textfield__input" placeholder="City or province" />
                  </div>
                  <p v-if="addressErrors.city" class="md-textfield__error">{{ addressErrors.city }}</p>
                </div>

                <div class="md-textfield">
                  <label for="postal-code" class="md-textfield__label">Postal Code</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">pin</span>
                    <input type="text" id="postal-code" v-model="newAddress.postal_code" class="md-textfield__input" placeholder="Postal code" />
                  </div>
                </div>

                <div class="md-textfield">
                  <label for="country" class="md-textfield__label">Country</label>
                  <div class="md-textfield__container">
                    <span class="material-icons-round md-textfield__icon">public</span>
                    <input type="text" id="country" v-model="newAddress.country" class="md-textfield__input" placeholder="Vietnam" />
                  </div>
                </div>

                <div class="md-checkbox">
                  <input type="checkbox" id="address-default" v-model="newAddress.is_default" class="md-checkbox__input" />
                  <label for="address-default" class="md-checkbox__label">Set as default address</label>
                </div>
              </div>
              <div class="md-dialog__actions">
                <button @click="cancelAddressForm" class="md-button md-button--text">Cancel</button>
                <button @click="saveAddress" class="md-button md-button--filled">Save</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Current Orders section -->
      <div v-if="activeTab === 'current_orders'" class="md-tab-content">
        <div class="md-profile__section">
          <div class="md-profile__header">
            <h2 class="md-profile__section-title">Current Orders</h2>
          </div>
          
          <!-- Current Orders List -->
          <div v-if="currentOrders.length > 0" class="md-order-list">
            <div v-for="(order, index) in currentOrders" :key="index" class="md-order-card md-current-order-card">
              <div class="md-order-card__header">
                <div>
                  <h3 class="md-order-card__title">Order #{{ order.id }}</h3>
                  <p class="md-order-card__date">{{ formatDate(order.date) }}</p>
                </div>
                <div class="md-order-card__status" :class="`md-order-card__status--${order.status.toLowerCase()}`">
                  {{ order.status }}
                </div>
              </div>
              
              <!-- Order Tracking Section -->
              <div class="md-order-tracking">
                <div class="md-order-tracking__progress">
                  <div class="md-order-tracking__step" :class="{'md-order-tracking__step--active': order.status === 'Confirmed' || order.status === 'Preparing' || order.status === 'On the Way' || order.status === 'Delivered'}">
                    <div class="md-order-tracking__icon-container">
                      <span class="material-icons-round md-order-tracking__icon">assignment_turned_in</span>
                    </div>
                    <p class="md-order-tracking__label">Confirmed</p>
                  </div>
                  <div class="md-order-tracking__connector"></div>
                  <div class="md-order-tracking__step" :class="{'md-order-tracking__step--active': order.status === 'Preparing' || order.status === 'On the Way' || order.status === 'Delivered'}">
                    <div class="md-order-tracking__icon-container">
                      <span class="material-icons-round md-order-tracking__icon">restaurant</span>
                    </div>
                    <p class="md-order-tracking__label">Preparing</p>
                  </div>
                  <div class="md-order-tracking__connector"></div>
                  <div class="md-order-tracking__step" :class="{'md-order-tracking__step--active': order.status === 'On the Way' || order.status === 'Delivered'}">
                    <div class="md-order-tracking__icon-container">
                      <span class="material-icons-round md-order-tracking__icon">delivery_dining</span>
                    </div>
                    <p class="md-order-tracking__label">On the Way</p>
                  </div>
                  <div class="md-order-tracking__connector"></div>
                  <div class="md-order-tracking__step" :class="{'md-order-tracking__step--active': order.status === 'Delivered'}">
                    <div class="md-order-tracking__icon-container">
                      <span class="material-icons-round md-order-tracking__icon">check_circle</span>
                    </div>
                    <p class="md-order-tracking__label">Delivered</p>
                  </div>
                </div>
              </div>
              
              <div class="md-order-card__items">
                <div v-for="(item, itemIndex) in order.items" :key="itemIndex" class="md-order-card__item">
                  <span class="md-order-card__item-quantity">{{ item.quantity }}x</span>
                  <span class="md-order-card__item-name">{{ item.name }}</span>
                  <span class="md-order-card__item-price">${{ (item.price * item.quantity).toFixed(2) }}</span>
                </div>
              </div>
              <div class="md-order-card__footer">
                <div class="md-order-card__total">
                  <span class="md-order-card__total-label">Total:</span>
                  <span class="md-order-card__total-value">${{ order.total.toFixed(2) }}</span>
                </div>
                <button class="md-button md-button--filled" @click="viewOrderDetails(order.id)">
                  <span class="material-icons-round">visibility</span>
                  Track Order
                </button>
              </div>
            </div>
          </div>
          
          <!-- Empty State -->
          <div v-else class="md-empty-state">
            <span class="material-icons-round md-empty-state__icon">local_shipping</span>
            <h3 class="md-empty-state__title">No active orders</h3>
            <p class="md-empty-state__text">You don't have any orders currently being processed</p>
            <router-link to="/menu" class="md-button md-button--filled">
              <span class="material-icons-round">restaurant_menu</span>
              Order Now
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- Order History section -->
      <div v-if="activeTab === 'orders'" class="md-tab-content">
        <div class="md-profile__section">
          <div class="md-profile__header">
            <h2 class="md-profile__section-title">Order History</h2>
          </div>
          
          <!-- Orders List -->
          <div v-if="orders.length > 0" class="md-order-list">
            <div v-for="(order, index) in orders" :key="index" class="md-order-card">
              <div class="md-order-card__header">
                <div>
                  <h3 class="md-order-card__title">Order #{{ order.id }}</h3>
                  <p class="md-order-card__date">{{ formatDate(order.date) }}</p>
                </div>
                <div class="md-order-card__status" :class="`md-order-card__status--${order.status.toLowerCase()}`">
                  {{ order.status }}
                </div>
              </div>
              <div class="md-order-card__items">
                <div v-for="(item, itemIndex) in order.items" :key="itemIndex" class="md-order-card__item">
                  <span class="md-order-card__item-quantity">{{ item.quantity }}x</span>
                  <span class="md-order-card__item-name">{{ item.name }}</span>
                  <span class="md-order-card__item-price">${{ (item.price * item.quantity).toFixed(2) }}</span>
                </div>
              </div>
              <div class="md-order-card__footer">
                <div class="md-order-card__total">
                  <span class="md-order-card__total-label">Total:</span>
                  <span class="md-order-card__total-value">${{ order.total.toFixed(2) }}</span>
                </div>
                <button class="md-button md-button--outline" @click="viewOrderDetails(order.id)">
                  <span class="material-icons-round">receipt</span>
                  View Details
                </button>
              </div>
            </div>
          </div>
          
          <!-- Empty State -->
          <div v-else class="md-empty-state">
            <span class="material-icons-round md-empty-state__icon">receipt_long</span>
            <h3 class="md-empty-state__title">No order history</h3>
            <p class="md-empty-state__text">You haven't placed any orders yet</p>
            <router-link to="/menu" class="md-button md-button--filled">
              <span class="material-icons-round">restaurant_menu</span>
              Browse Menu
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Order Details Modal -->
  <div v-if="showOrderDetailsModal" class="md-order-details-modal">
    <div class="md-order-details-backdrop" @click="showOrderDetailsModal = false"></div>
    <div class="md-order-details-dialog">
      <div class="md-order-details-header">
        <h2 class="md-order-details-title">Order #{{ currentOrderDetails.id }}</h2>
        <button class="md-order-details-close" @click="showOrderDetailsModal = false">
          <span class="material-icons-round">close</span>
        </button>
      </div>
      <div class="md-order-details-content">
        <div class="md-order-details-section">
          <h3 class="md-order-details-section-title">Order Summary</h3>
          <div class="md-order-details-info">
            <div class="md-order-details-info-item">
              <p class="md-order-details-info-label">Order Date:</p>
              <p class="md-order-details-info-value">{{ formatDate(currentOrderDetails.date) }}</p>
            </div>
            <div class="md-order-details-info-item">
              <p class="md-order-details-info-label">Status:</p>
              <p class="md-order-details-status" :class="`md-order-details-status--${currentOrderDetails.status.toLowerCase()}`">
                {{ currentOrderDetails.status }}
              </p>
            </div>
          </div>
        </div>
        <div class="md-order-details-section">
          <h3 class="md-order-details-section-title">Order Items</h3>
          <div class="md-order-details-items">
            <div class="md-order-details-items-header">
              <span>Quantity</span>
              <span>Item</span>
              <span>Price</span>
            </div>
            <div v-for="(item, index) in currentOrderDetails.items" :key="index" class="md-order-details-item">
              <span class="md-order-details-item-quantity">{{ item.quantity }}x</span>
              <span class="md-order-details-item-name">{{ item.name }}</span>
              <span class="md-order-details-item-price">${{ (item.price * item.quantity).toFixed(2) }}</span>
            </div>
          </div>
          <div class="md-order-details-summary">
            <div class="md-order-details-total">
              <span>Total:</span>
              <span>${{ currentOrderDetails.total.toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="md-order-details-actions">
        <button class="md-button md-button--filled" @click="showOrderDetailsModal = false">
          <span class="material-icons-round">close</span>
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import apiService from '../services/ApiService';

export default {
  name: 'Profile',
  
  data() {
    return {
      defaultAvatarUrl: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48Y2lyY2xlIGN4PSIxMjgiIGN5PSIxMjgiIHI9IjEyMCIgZmlsbD0iI2U5ZWRmOCIvPjxjaXJjbGUgY3g9IjEyOCIgY3k9IjEwNSIgcj0iNDUiIGZpbGw9IiM5MGEzYmYiLz48cGF0aCBkPSJNNzMgMTk2YzAtMzYuMjUgMjguMTgtNjEgNTUtNjFzNTUgMjQuNzUgNTUgNjEiIGZpbGw9IiM5MGEzYmYiLz48L3N2Zz4=',
      activeTab: 'profile',
      isEditing: false,
      profileData: {
        id: null,
        name: '',
        email: '',
        phone: '',
        birth: '',
        avatar: null,
        addresses: [],
      },
      editForm: {
        name: '',
        email: '',
        phone: '',
      },
      formErrors: {
        name: '',
        email: '',
        phone: '',
      },
      addresses: [
        // Sample addresses for demonstration
        {
          label: 'Home',
          street: '123 Main St',
          city: 'Ho Chi Minh City',
          zipCode: '70000',
          isDefault: true
        },
        {
          label: 'Work',
          street: '456 Office Blvd',
          city: 'Hanoi',
          zipCode: '10000',
          isDefault: false
        }
      ],
      orders: [
        // Sample orders for demonstration
        {
          id: '1001',
          date: '2025-04-15T14:30:00',
          status: 'Delivered',
          items: [
            { name: 'Beef Taco', quantity: 2, price: 8.99 },
            { name: 'Chicken Burrito', quantity: 1, price: 10.99 }
          ],
          total: 28.97
        },
        {
          id: '1002',
          date: '2025-04-10T18:45:00',
          status: 'Processing',
          items: [
            { name: 'Nachos Supreme', quantity: 1, price: 12.99 },
            { name: 'Quesadilla', quantity: 2, price: 9.99 }
          ],
          total: 32.97
        }
      ],
      currentOrders: [
        // Sample current orders for demonstration
        {
          id: '1003',
          date: '2025-04-18T12:30:00',
          status: 'Confirmed',
          items: [
            { name: 'Veggie Burrito', quantity: 1, price: 9.99 },
            { name: 'Churros', quantity: 2, price: 4.99 }
          ],
          total: 19.97
        },
        {
          id: '1004',
          date: '2025-04-19T08:15:00',
          status: 'Preparing',
          items: [
            { name: 'Breakfast Tacos', quantity: 3, price: 7.99 },
            { name: 'Coffee', quantity: 1, price: 2.99 }
          ],
          total: 26.96
        }
      ],
      newAddress: {
        label: '',
        street: '',
        city: '',
        zipCode: '',
        isDefault: false
      },
      addressErrors: {
        street: '',
        city: '',
        zipCode: ''
      },
      isAddingAddress: false,
      isEditingAddress: false,
      currentAddressIndex: -1,
      // Add order details modal properties
      showOrderDetailsModal: false,
      currentOrderDetails: null,
    }
  },
  
  computed: {
    ...mapState({
      user: state => state.user
    })
  },
  
  created() {
    // Check if a tab is specified in URL query parameters
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    if (tabParam && ['profile', 'addresses', 'current_orders', 'orders'].includes(tabParam)) {
      this.activeTab = tabParam;
    }
    
    this.loadUserProfile();
  },
  
  methods: {
    ...mapMutations(['setUser']),
    
    async loadUserProfile() {
      try {
        // For now, use the user from the store
        if (this.user && this.user.user_id) {
          // Try to load complete profile from API
          try {
            const profile = await apiService.getUserProfile(this.user.user_id);
            this.profileData = {
              id: profile.user_id,
              name: profile.user_name,
              email: profile.user_email,
              phone: profile.user_phone,
              birth: profile.user_birth,
              avatar: profile.user_avatar || null
            };
          } catch (apiError) {
            console.log('Could not fetch profile from API, using store data', apiError);
            // Fallback to data from store
            this.profileData = {
              id: this.user.user_id,
              name: this.user.user_name,
              email: '',
              phone: '',
              birth: '',
              avatar: null
            };
          }
          
          // Initialize the edit form with current values
          this.editForm = {
            name: this.profileData.name,
            email: this.profileData.email,
            phone: this.profileData.phone,
          };
          
          // Load addresses, orders, and current shipping orders
          this.loadUserAddresses();
          this.loadUserOrders();
          this.loadCurrentShippingOrders();
        }
      } catch (error) {
        console.error('Error loading profile:', error);
      }
    },
    
    async loadUserAddresses() {
      if (!this.user || !this.user.user_id) return;
      
      try {
        // Try to load addresses from API
        const addresses = await apiService.getUserAddresses(this.user.user_id);
        // If successful, replace sample data with actual data
        if (addresses && Array.isArray(addresses)) {
          this.addresses = addresses;
        }
      } catch (error) {
        console.log('Could not fetch addresses from API, using sample data', error);
        // Keep using the sample addresses data
      }
    },
    
    async loadUserOrders() {
      if (!this.user || !this.user.user_id) return;
      
      try {
        // Try to load orders from API
        const orders = await apiService.getUserOrders(this.user.user_id);
        // If successful, replace sample data with actual data
        if (orders && Array.isArray(orders)) {
          this.orders = orders;
        }
      } catch (error) {
        console.log('Could not fetch orders from API, using sample data', error);
        // Keep using the sample orders data
      }
    },
    
    async loadCurrentShippingOrders() {
      if (!this.user || !this.user.user_id) return;
      
      try {
        // Try to load current shipping orders from API
        const currentOrders = await apiService.getCurrentShippingOrders(this.user.user_id);
        // If successful, replace sample data with actual data
        if (currentOrders && Array.isArray(currentOrders)) {
          this.currentOrders = currentOrders;
        }
      } catch (error) {
        console.log('Could not fetch current shipping orders from API, using sample data', error);
        // Keep using the sample current orders data
      }
    },
    
    startEditing() {
      this.isEditing = true;
      // Reset the edit form with current values
      this.editForm = {
        name: this.profileData.name,
        email: this.profileData.email,
        phone: this.profileData.phone,
      };
      this.formErrors = { name: '', email: '', phone: '' };
    },
    
    cancelEditing() {
      this.isEditing = false;
      this.formErrors = { name: '', email: '', phone: '' };
    },
    
    async saveProfile() {
      // Validate form
      this.validateForm();
      
      // If there are errors, don't proceed
      if (this.formErrors.name || this.formErrors.email || this.formErrors.phone) {
        return;
      }
      
      try {
        if (this.user && this.user.user_id) {
          // Prepare data for API call
          const profileData = {
            user_name: this.editForm.name,
            user_email: this.editForm.email,
            user_phone: this.editForm.phone
          };
          
          try {
            // Try to update via API
            await apiService.updateUserProfile(this.user.user_id, profileData);
          } catch (apiError) {
            console.log('Could not update profile via API:', apiError);
            // Continue with local update even if API fails
          }
        }
        
        // Update local data
        this.profileData.name = this.editForm.name;
        this.profileData.email = this.editForm.email;
        this.profileData.phone = this.editForm.phone;
        
        // Update user in the store
        const updatedUser = {
          ...this.user,
          user_name: this.editForm.name
        };
        this.setUser(updatedUser);
        
        this.isEditing = false;
      } catch (error) {
        console.error('Error saving profile:', error);
      }
    },
    
    validateForm() {
      // Validate name
      if (!this.editForm.name) {
        this.formErrors.name = 'Name is required';
      } else {
        this.formErrors.name = '';
      }
      
      // Validate email
      if (!this.editForm.email) {
        this.formErrors.email = 'Email is required';
      } else if (!/[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$/.test(this.editForm.email)) {
        this.formErrors.email = 'Email must be valid';
      } else {
        this.formErrors.email = '';
      }
      
      // Validate phone
      if (!this.editForm.phone) {
        this.formErrors.phone = 'Phone is required';
      } else {
        const phoneClean = this.editForm.phone.replace(/[\s-]/g, '');
        const validPlusFormat = /^\+84\d{9}$/.test(phoneClean);
        const validZeroFormat = /^0\d{9}$/.test(phoneClean);
        
        if (!validPlusFormat && !validZeroFormat) {
          this.formErrors.phone = 'Phone number must start with +84 (11 digits total) or 0 (10 digits total)';
        } else if (!/^[+0-9]+$/.test(phoneClean)) {
          this.formErrors.phone = 'Phone numbers can only contain digits and + sign if international format';
        } else {
          this.formErrors.phone = '';
        }
      }
    },
    
    handleAvatarChange(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // For now, just preview the image locally
      const reader = new FileReader();
      reader.onload = (e) => {
        this.profileData.avatar = e.target.result;
      };
      reader.readAsDataURL(file);
    },
    
    formatDate(dateString) {
      if (!dateString) return 'Not specified';
      
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date);
    },
    
    // Address management methods
    startAddingAddress() {
      this.isAddingAddress = true;
      this.isEditingAddress = false;
      this.currentAddressIndex = -1;
      this.newAddress = {
        address_label: 'Home',
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        address_line2: '',
        district: '',
        city: '',
        postal_code: '',
        country: 'Vietnam',
        is_default: false
      };
      this.addressErrors = {
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        city: ''
      };
    },
    
    editAddress(index) {
      this.isEditingAddress = true;
      this.isAddingAddress = false;
      this.currentAddressIndex = index;

      // Copy address to edit form
      this.newAddress = { ...this.addresses[index] };
      this.addressErrors = {
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        city: ''
      };
    },
    
    cancelAddressForm() {
      this.isAddingAddress = false;
      this.isEditingAddress = false;
      this.currentAddressIndex = -1;
    },
    
    validateAddressForm() {
      let isValid = true;

      // Validate recipient name
      if (!this.newAddress.recipient_name || !this.newAddress.recipient_name.trim()) {
        this.addressErrors.recipient_name = 'Recipient name is required';
        isValid = false;
      } else {
        this.addressErrors.recipient_name = '';
      }

      // Validate recipient phone
      if (!this.newAddress.recipient_phone || !this.newAddress.recipient_phone.trim()) {
        this.addressErrors.recipient_phone = 'Phone number is required';
        isValid = false;
      } else {
        this.addressErrors.recipient_phone = '';
      }

      // Validate street address
      if (!this.newAddress.address_line1 || !this.newAddress.address_line1.trim()) {
        this.addressErrors.address_line1 = 'Street address is required';
        isValid = false;
      } else {
        this.addressErrors.address_line1 = '';
      }

      // Validate city
      if (!this.newAddress.city || !this.newAddress.city.trim()) {
        this.addressErrors.city = 'City is required';
        isValid = false;
      } else {
        this.addressErrors.city = '';
      }

      return isValid;
    },
    
    async saveAddress() {
      if (!this.validateAddressForm()) {
        return;
      }
      
      // Handle default address logic
      if (this.newAddress.is_default) {
        // Unset any existing default address
        this.addresses.forEach(address => {
          address.is_default = false;
        });
      }
      
      try {
        if (this.user && this.user.user_id) {
          // Prepare address data for API
          const addressData = { ...this.newAddress };
          
          if (this.isEditingAddress) {
            // Try to update via API
            const addressId = this.addresses[this.currentAddressIndex].id;
            if (addressId) {
              try {
                await apiService.updateUserAddress(this.user.user_id, addressId, addressData);
              } catch (apiError) {
                console.log('Could not update address via API:', apiError);
              }
            }
            // Update existing address locally
            this.addresses[this.currentAddressIndex] = { ...this.newAddress };
          } else {
            // Try to add via API
            try {
              const newAddress = await apiService.addUserAddress(this.user.user_id, addressData);
              // If API call successful, use returned address with ID
              if (newAddress && newAddress.id) {
                this.addresses.push(newAddress);
              } else {
                // Fallback to local add
                this.addresses.push({ ...this.newAddress });
              }
            } catch (apiError) {
              console.log('Could not add address via API:', apiError);
              // Fallback to local add
              this.addresses.push({ ...this.newAddress });
            }
          }
        } else {
          // No user ID, just update locally
          if (this.isEditingAddress) {
            this.addresses[this.currentAddressIndex] = { ...this.newAddress };
          } else {
            this.addresses.push({ ...this.newAddress });
          }
        }
        
        // If no addresses were marked as default, mark the first one
        if (this.addresses.length === 1) {
          this.addresses[0].is_default = true;
        }
        
        // Close the form
        this.cancelAddressForm();
      } catch (error) {
        console.error('Error saving address:', error);
      }
    },
    
    async deleteAddress(index) {
      const address = this.addresses[index];
      const wasDefault = address.is_default;
      
      try {
        // Try to delete via API if we have an address ID and user ID
        if (this.user && this.user.user_id && address.id) {
          try {
            await apiService.deleteUserAddress(this.user.user_id, address.id);
          } catch (apiError) {
            console.log('Could not delete address via API:', apiError);
          }
        }
        
        // Remove from local array
        this.addresses.splice(index, 1);
        
        // If we deleted the default address, set a new default
        if (wasDefault && this.addresses.length > 0) {
          this.addresses[0].is_default = true;

          // Update default status via API if possible
          if (this.user && this.user.user_id && this.addresses[0].id) {
            try {
              await apiService.updateUserAddress(
                this.addresses[0].id,
                { ...this.addresses[0], is_default: true }
              );
            } catch (apiError) {
              console.log('Could not update default address via API:', apiError);
            }
          }
        }
      } catch (error) {
        console.error('Error deleting address:', error);
      }
    },
    
    setDefaultAddress(index) {
      // Unset current default
      this.addresses.forEach(address => {
        address.is_default = false;
      });

      // Set new default
      this.addresses[index].is_default = true;
    },
    
    // Order management methods
    viewOrderDetails(orderId) {
      // Find the order
      const order = this.orders.find(o => o.id === orderId);
      if (order) {
        // Set the current order details and show modal
        this.currentOrderDetails = order;
        this.showOrderDetailsModal = true;
      }
    }
  }
}
</script>

<style scoped>
.md-profile {
  padding: 2rem 1rem;
}

.md-container {
  max-width: 1200px;
  margin: 0 auto;
}

.md-profile__title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--md-sys-color-on-surface);
}

.md-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  overflow-x: auto;
}

.md-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.md-tab:hover {
  color: var(--md-sys-color-on-surface);
  background-color: var(--md-sys-color-surface-variant);
}

.md-tab--active {
  color: var(--md-sys-color-primary);
  border-bottom-color: var(--md-sys-color-primary);
}

.md-tab-content {
  background: var(--md-sys-color-surface);
  border-radius: 16px;
  box-shadow: var(--md-elevation-1);
  overflow: hidden;
}

.md-profile__section {
  padding: 1.5rem;
}

.md-profile__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.md-profile__section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-button-group {
  display: flex;
  gap: 0.5rem;
}

.md-profile__user-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 768px) {
  .md-profile__user-info {
    flex-direction: row;
  }
}

.md-profile__avatar-container {
  flex-shrink: 0;
}

.md-profile__avatar {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--md-sys-color-surface-variant);
  box-shadow: var(--md-elevation-1);
}

.md-profile__avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-profile__avatar-edit {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  padding: 0.5rem 0;
}

.md-profile__avatar-edit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.md-profile__avatar-input {
  display: none;
}

.md-profile__details {
  flex-grow: 1;
}

.md-profile__info-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.md-profile__info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.md-profile__info-icon {
  color: var(--md-sys-color-primary);
  padding: 0.5rem;
  background-color: var(--md-sys-color-primary-container);
  border-radius: 50%;
}

.md-profile__info-content {
  flex-grow: 1;
}

.md-profile__info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
  margin: 0 0 0.25rem 0;
}

.md-profile__info-value {
  font-size: 1rem;
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-profile__edit-form {
  display: grid;
  gap: 1.5rem;
}

.md-textfield {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.md-textfield__label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
}

.md-textfield__container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: 8px;
  border: 1px solid var(--md-sys-color-outline);
}

.md-textfield__icon {
  color: var(--md-sys-color-on-surface-variant);
}

.md-textfield__input {
  flex-grow: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: var(--md-sys-color-on-surface);
  outline: none;
}

.md-textfield__error {
  font-size: 0.75rem;
  color: var(--md-sys-color-error);
  margin: 0;
}

.md-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 0.875rem;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.md-button--filled {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button--filled:hover {
  background-color: var(--md-sys-color-primary-hover, var(--md-sys-color-primary));
  box-shadow: var(--md-elevation-2);
}

.md-button--outline {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button--outline:hover {
  background-color: var(--md-sys-color-surface-variant);
}

.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  padding: 0.5rem 0.75rem;
}

.md-button--text:hover {
  background-color: var(--md-sys-color-surface-variant);
}

/* Address section styles */
.md-address-list {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.md-address-card {
  background-color: var(--md-sys-color-surface);
  border-radius: 12px;
  border: 1px solid var(--md-sys-color-outline-variant);
  padding: 1rem;
  position: relative;
  transition: all 0.2s ease;
}

.md-address-card:hover {
  box-shadow: var(--md-elevation-2);
  border-color: var(--md-sys-color-outline);
}

.md-address-card__badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-weight: 500;
}

.md-address-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--md-sys-color-on-surface);
}

.md-address-card__text {
  font-size: 0.875rem;
  margin: 0.25rem 0;
  color: var(--md-sys-color-on-surface-variant);
}

.md-address-card__actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 1rem;
  justify-content: flex-start;
}

/* Order History section styles */
.md-order-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.md-order-card {
  background-color: var(--md-sys-color-surface);
  border-radius: 12px;
  border: 1px solid var(--md-sys-color-outline-variant);
  padding: 1rem;
  transition: all 0.2s ease;
}

.md-order-card:hover {
  box-shadow: var(--md-elevation-2);
  border-color: var(--md-sys-color-outline);
}

.md-current-order-card {
  border-left: 4px solid var(--md-sys-color-primary);
}

/* Order tracking styles */
.md-order-tracking {
  margin: 1rem 0;
  padding: 0.5rem 0;
}

.md-order-tracking__progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.md-order-tracking__step {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  flex: 1;
}

.md-order-tracking__icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--md-sys-color-surface-variant);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.md-order-tracking__step--active .md-order-tracking__icon-container {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-order-tracking__icon {
  font-size: 20px;
  color: var(--md-sys-color-on-surface-variant);
}

.md-order-tracking__step--active .md-order-tracking__icon {
  color: var(--md-sys-color-on-primary);
}

.md-order-tracking__label {
  font-size: 0.75rem;
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
  text-align: center;
}

.md-order-tracking__step--active .md-order-tracking__label {
  color: var(--md-sys-color-primary);
  font-weight: 500;
}

.md-order-tracking__connector {
  flex-grow: 1;
  height: 2px;
  background-color: var(--md-sys-color-surface-variant);
  z-index: 0;
  margin: 0 -5px;
  position: relative;
  top: -20px;
}

.md-order-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-order-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--md-sys-color-on-surface);
}

.md-order-card__date {
  font-size: 0.875rem;
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
}

.md-order-card__status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
}

.md-order-card__status--delivered {
  background-color: var(--md-sys-color-tertiary-container, #d0f8c0);
  color: var(--md-sys-color-on-tertiary-container, #003a00);
}

.md-order-card__status--processing {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.md-order-card__status--cancelled {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

.md-order-card__items {
  margin-bottom: 1rem;
}

.md-order-card__item {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.md-order-card__item-quantity {
  flex: 0 0 2rem;
  color: var(--md-sys-color-primary);
  font-weight: 500;
}

.md-order-card__item-name {
  flex: 1;
  color: var(--md-sys-color-on-surface);
}

.md-order-card__item-price {
  flex: 0 0 4rem;
  text-align: right;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-order-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.75rem;
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.md-order-card__total {
  display: flex;
  flex-direction: column;
}

.md-order-card__total-label {
  font-size: 0.75rem;
  color: var(--md-sys-color-on-surface-variant);
}

.md-order-card__total-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
}

/* Dialog and overlay styles */
.md-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.md-dialog {
  position: relative;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: var(--md-sys-color-surface);
  border-radius: 16px;
  box-shadow: var(--md-elevation-3);
  z-index: 11;
  display: flex;
  flex-direction: column;
}

.md-dialog__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-dialog__title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.md-dialog__close {
  background: transparent;
  border: none;
  color: var(--md-sys-color-on-surface-variant);
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.md-dialog__close:hover {
  background-color: var(--md-sys-color-surface-variant);
}

.md-dialog__content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.md-dialog__actions {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  gap: 0.5rem;
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

/* Empty state styles */
.md-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.md-empty-state__icon {
  font-size: 3rem;
  color: var(--md-sys-color-outline);
  margin-bottom: 1rem;
}

.md-empty-state__title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--md-sys-color-on-surface);
}

.md-empty-state__text {
  font-size: 0.875rem;
  margin: 0 0 1.5rem 0;
  color: var(--md-sys-color-on-surface-variant);
}

/* Checkbox styles */
.md-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.md-checkbox__input {
  width: 18px;
  height: 18px;
  accent-color: var(--md-sys-color-primary);
}

.md-checkbox__label {
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface);
}

/* Order Details Modal Styles */
.md-order-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.md-order-details-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.md-order-details-dialog {
  position: relative;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: var(--md-sys-color-surface);
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.14);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.md-order-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-order-details-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-order-details-id {
  font-weight: 600;
}

.md-order-details-close {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.md-order-details-close:hover {
  background-color: var(--md-sys-color-surface-variant);
}

.md-order-details-content {
  padding: 24px;
}

.md-order-details-section {
  margin-bottom: 24px;
}

.md-order-details-section-title {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 16px 0;
  color: var(--md-sys-color-on-surface-variant);
}

.md-order-details-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.md-order-details-info-item {
  margin-bottom: 12px;
}

.md-order-details-info-label {
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface-variant);
  margin: 0 0 4px 0;
}

.md-order-details-info-value {
  font-size: 1rem;
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-order-details-status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

.md-order-details-status--delivered {
  background-color: rgba(0, 180, 0, 0.12);
  color: rgb(0, 120, 0);
}

.md-order-details-status--processing {
  background-color: rgba(33, 150, 243, 0.12);
  color: rgb(21, 101, 192);
}

.md-order-details-status--confirmed {
  background-color: rgba(156, 39, 176, 0.12);
  color: rgb(106, 27, 154);
}

.md-order-details-status--preparing {
  background-color: rgba(255, 152, 0, 0.12);
  color: rgb(230, 81, 0);
}

.md-order-details-items {
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: 8px;
  overflow: hidden;
}

.md-order-details-items-header {
  display: grid;
  grid-template-columns: 70px 1fr auto;
  padding: 12px 16px;
  background-color: var(--md-sys-color-surface-variant);
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
}

.md-order-details-item {
  display: grid;
  grid-template-columns: 70px 1fr auto;
  padding: 12px 16px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-order-details-item:last-child {
  border-bottom: none;
}

.md-order-details-item-quantity {
  font-weight: 500;
}

.md-order-details-item-name {
  color: var(--md-sys-color-on-surface);
}

.md-order-details-item-price {
  text-align: right;
  font-weight: 500;
}

.md-order-details-summary {
  margin-top: 24px;
}

.md-order-details-total {
  display: flex;
  justify-content: space-between;
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid var(--md-sys-color-outline-variant);
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
}

.md-order-details-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid var(--md-sys-color-outline-variant);
}
</style>
