const AddressModel = require('../models/AddressModel');

// Get all addresses for a user
exports.getUserAddresses = (req, res) => {
    const userId = req.params.userId;
    
    AddressModel.getUserAddresses(userId, (err, addresses) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving addresses",
                error: err
            });
        } else {
            res.json(addresses);
        }
    });
};

// Get default address for a user
exports.getDefaultAddress = (req, res) => {
    const userId = req.params.userId;
    
    AddressModel.getDefaultAddress(userId, (err, address) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving default address",
                error: err
            });
        } else {
            res.json(address || null);
        }
    });
};

// Add a new address
exports.addAddress = (req, res) => {
    // Validate request
    if (!req.body) {
        return res.status(400).json({
            message: "Address content cannot be empty"
        });
    }
    
    // Create address object
    const addressData = {
        user_id: req.body.user_id,
        label: req.body.label || '',
        street: req.body.street,
        city: req.body.city,
        zip_code: req.body.zip_code,
        is_default: req.body.is_default ? 1 : 0
    };
    
    // Save address
    AddressModel.addAddress(addressData, (err, address) => {
        if (err) {
            res.status(500).json({
                message: "Error adding address",
                error: err
            });
        } else {
            res.status(201).json(address);
        }
    });
};

// Update an address
exports.updateAddress = (req, res) => {
    // Validate request
    if (!req.body) {
        return res.status(400).json({
            message: "Address content cannot be empty"
        });
    }
    
    const id = req.params.id;
    
    // Create address object
    const addressData = {
        user_id: req.body.user_id,
        label: req.body.label || '',
        street: req.body.street,
        city: req.body.city,
        zip_code: req.body.zip_code,
        is_default: req.body.is_default ? 1 : 0
    };
    
    // Update address
    AddressModel.updateAddress(id, addressData, (err, address) => {
        if (err) {
            res.status(500).json({
                message: "Error updating address",
                error: err
            });
        } else {
            res.json(address);
        }
    });
};

// Delete an address
exports.deleteAddress = (req, res) => {
    const id = req.params.id;
    
    AddressModel.deleteAddress(id, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error deleting address",
                error: err
            });
        } else {
            res.json({ message: "Address deleted successfully", id: id });
        }
    });
};

// Set an address as default
exports.setDefaultAddress = (req, res) => {
    const userId = req.params.userId;
    const addressId = req.params.addressId;
    
    AddressModel.setDefaultAddress(userId, addressId, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error setting default address",
                error: err
            });
        } else {
            res.json({ message: "Default address set successfully", id: addressId });
        }
    });
};
