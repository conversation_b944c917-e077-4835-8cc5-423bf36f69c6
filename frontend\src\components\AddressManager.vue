<template>
  <div class="md-address-manager">
    <!-- Header -->
    <div class="md-address-manager__header">
      <h2 class="md-address-manager__title">
        <i class="material-icons">location_on</i>
        My Addresses
      </h2>
      <button class="md-button md-button--primary" @click="openAddAddressModal">
        <i class="material-icons">add</i>
        Add New Address
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="md-loading">
      <div class="md-spinner"></div>
      <p>Loading addresses...</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="addresses.length === 0" class="md-empty-state">
      <i class="material-icons">location_off</i>
      <h3>No Addresses Yet</h3>
      <p>Add your first delivery address to get started</p>
      <button class="md-button md-button--primary" @click="openAddAddressModal">
        <i class="material-icons">add</i>
        Add Address
      </button>
    </div>

    <!-- Address List -->
    <div v-else class="md-address-list">
      <div v-for="address in addresses" :key="address.address_id" 
           class="md-address-card" :class="{'md-address-card--default': address.is_default}">
        
        <!-- Address Header -->
        <div class="md-address-card__header">
          <div class="md-address-card__label-group">
            <span class="md-address-card__label">{{ address.address_label }}</span>
            <span v-if="address.is_default" class="md-address-card__default-badge">
              <i class="material-icons">star</i>
              Default
            </span>
          </div>
          <div class="md-address-card__actions">
            <button class="md-button md-button--text md-button--small" @click="editAddress(address)">
              <i class="material-icons">edit</i>
            </button>
            <button v-if="!address.is_default" 
                    class="md-button md-button--text md-button--small" 
                    @click="setAsDefault(address)">
              <i class="material-icons">star_border</i>
            </button>
            <button class="md-button md-button--text md-button--small md-button--danger" 
                    @click="deleteAddress(address)">
              <i class="material-icons">delete</i>
            </button>
          </div>
        </div>

        <!-- Address Content -->
        <div class="md-address-card__content">
          <div class="md-address-card__recipient">
            <strong>{{ address.recipient_name }}</strong>
            <span class="md-address-card__phone">{{ address.recipient_phone }}</span>
          </div>
          <div class="md-address-card__address">
            <p>{{ address.address_line1 }}</p>
            <p v-if="address.address_line2">{{ address.address_line2 }}</p>
            <p>{{ [address.district, address.city].filter(Boolean).join(', ') }}</p>
            <p v-if="address.postal_code">{{ address.postal_code }}, {{ address.country }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Address Modal -->
    <div v-if="showAddressModal" class="md-dialog-overlay" @click.self="closeAddressModal">
      <div class="md-dialog md-dialog--large">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">
            {{ isEditing ? 'Edit Address' : 'Add New Address' }}
          </h2>
          <button @click="closeAddressModal" class="md-dialog__close">
            <i class="material-icons">close</i>
          </button>
        </div>
        
        <div class="md-dialog__content">
          <form @submit.prevent="saveAddress" class="md-address-form">
            <!-- Address Label -->
            <div class="md-form-field">
              <label class="md-form-field__label">Address Label</label>
              <select v-model="addressForm.address_label" class="md-form-field__select">
                <option value="Home">🏠 Home</option>
                <option value="Work">🏢 Work</option>
                <option value="Other">📍 Other</option>
              </select>
            </div>

            <!-- Recipient Info -->
            <div class="md-form-row">
              <div class="md-form-field">
                <label class="md-form-field__label">Recipient Name *</label>
                <input v-model="addressForm.recipient_name" 
                       type="text" 
                       class="md-form-field__input"
                       placeholder="Full name"
                       required>
              </div>
              <div class="md-form-field">
                <label class="md-form-field__label">Phone Number *</label>
                <input v-model="addressForm.recipient_phone" 
                       type="tel" 
                       class="md-form-field__input"
                       placeholder="0123456789"
                       required>
              </div>
            </div>

            <!-- Address Lines -->
            <div class="md-form-field">
              <label class="md-form-field__label">Street Address *</label>
              <input v-model="addressForm.address_line1" 
                     type="text" 
                     class="md-form-field__input"
                     placeholder="House number, street name"
                     required>
            </div>

            <div class="md-form-field">
              <label class="md-form-field__label">Ward/Commune</label>
              <input v-model="addressForm.address_line2" 
                     type="text" 
                     class="md-form-field__input"
                     placeholder="Ward, commune">
            </div>

            <!-- District and City -->
            <div class="md-form-row">
              <div class="md-form-field">
                <label class="md-form-field__label">District</label>
                <input v-model="addressForm.district" 
                       type="text" 
                       class="md-form-field__input"
                       placeholder="District">
              </div>
              <div class="md-form-field">
                <label class="md-form-field__label">City/Province *</label>
                <input v-model="addressForm.city" 
                       type="text" 
                       class="md-form-field__input"
                       placeholder="City or province"
                       required>
              </div>
            </div>

            <!-- Postal Code and Country -->
            <div class="md-form-row">
              <div class="md-form-field">
                <label class="md-form-field__label">Postal Code</label>
                <input v-model="addressForm.postal_code" 
                       type="text" 
                       class="md-form-field__input"
                       placeholder="Postal code">
              </div>
              <div class="md-form-field">
                <label class="md-form-field__label">Country</label>
                <input v-model="addressForm.country" 
                       type="text" 
                       class="md-form-field__input"
                       placeholder="Vietnam">
              </div>
            </div>

            <!-- Default Address -->
            <div class="md-form-field">
              <label class="md-checkbox">
                <input type="checkbox" v-model="addressForm.is_default">
                <span class="md-checkbox__checkmark"></span>
                Set as default address
              </label>
            </div>

            <!-- Error Messages -->
            <div v-if="errors.length > 0" class="md-error-list">
              <p v-for="error in errors" :key="error" class="md-error-message">{{ error }}</p>
            </div>
          </form>
        </div>

        <div class="md-dialog__actions">
          <button type="button" class="md-button md-button--text" @click="closeAddressModal">
            Cancel
          </button>
          <button type="submit" class="md-button md-button--primary" @click="saveAddress" :disabled="saving">
            <i class="material-icons" v-if="!saving">{{ isEditing ? 'save' : 'add' }}</i>
            <i class="material-icons spin" v-else>sync</i>
            {{ saving ? 'Saving...' : (isEditing ? 'Update' : 'Add') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import axios from "axios";

export default {
  name: 'AddressManager',
  
  data() {
    return {
      addresses: [],
      loading: false,
      saving: false,
      showAddressModal: false,
      isEditing: false,
      editingAddress: null,
      errors: [],
      addressForm: {
        address_label: 'Home',
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        address_line2: '',
        district: '',
        city: '',
        postal_code: '',
        country: 'Vietnam',
        is_default: false
      }
    };
  },

  computed: {
    ...mapState(["user"]),
  },

  mounted() {
    if (!this.user) {
      this.$router.push('/login');
      return;
    }
    this.loadAddresses();
  },

  methods: {
    async loadAddresses() {
      try {
        this.loading = true;
        const response = await axios.get(`/api/users/${this.user.user_id}/addresses`);
        this.addresses = response.data;
      } catch (error) {
        console.error('Error loading addresses:', error);
      } finally {
        this.loading = false;
      }
    },

    openAddAddressModal() {
      this.isEditing = false;
      this.editingAddress = null;
      this.resetForm();
      this.showAddressModal = true;
    },

    editAddress(address) {
      this.isEditing = true;
      this.editingAddress = address;
      this.addressForm = {
        address_label: address.address_label,
        recipient_name: address.recipient_name,
        recipient_phone: address.recipient_phone,
        address_line1: address.address_line1,
        address_line2: address.address_line2 || '',
        district: address.district || '',
        city: address.city,
        postal_code: address.postal_code || '',
        country: address.country || 'Vietnam',
        is_default: Boolean(address.is_default)
      };
      this.showAddressModal = true;
    },

    closeAddressModal() {
      this.showAddressModal = false;
      this.errors = [];
      this.resetForm();
    },

    resetForm() {
      this.addressForm = {
        address_label: 'Home',
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        address_line2: '',
        district: '',
        city: '',
        postal_code: '',
        country: 'Vietnam',
        is_default: false
      };
    },

    validateForm() {
      this.errors = [];
      
      if (!this.addressForm.recipient_name.trim()) {
        this.errors.push('Recipient name is required');
      }
      
      if (!this.addressForm.recipient_phone.trim()) {
        this.errors.push('Phone number is required');
      }
      
      if (!this.addressForm.address_line1.trim()) {
        this.errors.push('Street address is required');
      }
      
      if (!this.addressForm.city.trim()) {
        this.errors.push('City is required');
      }
      
      return this.errors.length === 0;
    },

    async saveAddress() {
      if (!this.validateForm()) return;
      
      try {
        this.saving = true;
        
        if (this.isEditing) {
          await axios.put(`/api/addresses/${this.editingAddress.address_id}`, this.addressForm);
        } else {
          await axios.post(`/api/users/${this.user.user_id}/addresses`, this.addressForm);
        }
        
        this.closeAddressModal();
        this.loadAddresses();
      } catch (error) {
        console.error('Error saving address:', error);
        this.errors.push('Error saving address. Please try again.');
      } finally {
        this.saving = false;
      }
    },

    async setAsDefault(address) {
      try {
        await axios.put(`/api/users/${this.user.user_id}/addresses/${address.address_id}/default`);
        this.loadAddresses();
      } catch (error) {
        console.error('Error setting default address:', error);
      }
    },

    async deleteAddress(address) {
      if (confirm(`Are you sure you want to delete this address?\n\n${address.address_line1}, ${address.city}`)) {
        try {
          await axios.delete(`/api/addresses/${address.address_id}`);
          this.loadAddresses();
        } catch (error) {
          console.error('Error deleting address:', error);
        }
      }
    }
  }
};
</script>

<style scoped>
.md-address-manager {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.md-address-manager__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.md-address-manager__title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.md-loading,
.md-empty-state {
  text-align: center;
  padding: 48px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.md-loading .md-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.md-empty-state i {
  font-size: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

.md-empty-state h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.md-empty-state p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.md-address-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.md-address-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.md-address-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.md-address-card--default {
  border: 2px solid #ff6b35;
}

.md-address-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.md-address-card__label-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.md-address-card__label {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.md-address-card__default-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background-color: #ff6b35;
  color: white;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.md-address-card__actions {
  display: flex;
  gap: 4px;
}

.md-address-card__content {
  padding: 20px;
}

.md-address-card__recipient {
  margin-bottom: 12px;
}

.md-address-card__recipient strong {
  display: block;
  font-size: 16px;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.md-address-card__phone {
  color: #666;
  font-size: 14px;
}

.md-address-card__address p {
  margin: 4px 0;
  color: #333;
  line-height: 1.5;
}

.md-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.md-button--text {
  background: transparent;
  color: #666;
}

.md-button--text:hover {
  background-color: #f5f5f5;
  color: #333;
}

.md-button--small {
  padding: 6px 12px;
  font-size: 12px;
}

.md-button--primary {
  background-color: #ff6b35;
  color: white;
}

.md-button--primary:hover {
  background-color: #e55a2b;
}

.md-button--danger {
  color: #d32f2f;
}

.md-button--danger:hover {
  background-color: #ffebee;
}

.md-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.md-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.md-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.md-dialog--large {
  max-width: 700px;
}

.md-dialog__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.md-dialog__title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.md-dialog__close {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  color: #666;
  transition: background-color 0.2s;
}

.md-dialog__close:hover {
  background-color: #f5f5f5;
}

.md-dialog__content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.md-dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e0e0e0;
}

.md-address-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.md-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.md-form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.md-form-field__label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.md-form-field__input,
.md-form-field__select {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.2s;
}

.md-form-field__input:focus,
.md-form-field__select:focus {
  outline: none;
  border-color: #ff6b35;
}

.md-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.md-checkbox input {
  display: none;
}

.md-checkbox__checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s;
}

.md-checkbox input:checked + .md-checkbox__checkmark {
  background-color: #ff6b35;
  border-color: #ff6b35;
}

.md-checkbox input:checked + .md-checkbox__checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.md-error-list {
  background-color: #ffebee;
  border: 1px solid #f44336;
  border-radius: 8px;
  padding: 12px;
}

.md-error-message {
  margin: 0;
  color: #d32f2f;
  font-size: 14px;
}

.spin {
  animation: spin 1s linear infinite;
}

@media (max-width: 768px) {
  .md-address-manager {
    padding: 16px;
  }

  .md-address-manager__header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .md-address-card__header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .md-address-card__actions {
    width: 100%;
    justify-content: flex-end;
  }

  .md-form-row {
    grid-template-columns: 1fr;
  }

  .md-dialog {
    width: 95%;
    margin: 16px;
  }
}
</style>
