const db = require('../config/database');

// Get all addresses for a user
const getUserAddresses = (userId, result) => {
    db.query('SELECT * FROM user_address WHERE user_id = ?', [userId], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Get default address for a user
const getDefaultAddress = (userId, result) => {
    db.query('SELECT * FROM user_address WHERE user_id = ? AND is_default = 1', [userId], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results[0]);
        }
    });
};

// Add a new address
const addAddress = (addressData, result) => {
    // If this is set as default, first reset any existing default
    if (addressData.is_default) {
        db.query('UPDATE user_address SET is_default = 0 WHERE user_id = ?', [addressData.user_id], (err) => {
            if (err) {
                console.log(err);
                result(err, null);
                return;
            }
            
            // Now insert the new address
            insertAddress(addressData, result);
        });
    } else {
        // If not default, just insert
        insertAddress(addressData, result);
    }
};

// Helper function to insert address
const insertAddress = (addressData, result) => {
    db.query('INSERT INTO user_address SET ?', addressData, (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, { id: results.insertId, ...addressData });
        }
    });
};

// Update an address
const updateAddress = (id, addressData, result) => {
    // If this is set as default, first reset any existing default
    if (addressData.is_default) {
        db.query('UPDATE user_address SET is_default = 0 WHERE user_id = ?', [addressData.user_id], (err) => {
            if (err) {
                console.log(err);
                result(err, null);
                return;
            }
            
            // Now update the address
            updateAddressRecord(id, addressData, result);
        });
    } else {
        // If not default, just update
        updateAddressRecord(id, addressData, result);
    }
};

// Helper function to update address
const updateAddressRecord = (id, addressData, result) => {
    db.query(
        'UPDATE user_address SET label = ?, street = ?, city = ?, zip_code = ?, is_default = ? WHERE id = ?',
        [addressData.label, addressData.street, addressData.city, addressData.zip_code, addressData.is_default, id],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, { id: id, ...addressData });
            }
        }
    );
};

// Delete an address
const deleteAddress = (id, result) => {
    db.query('DELETE FROM user_address WHERE id = ?', [id], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Set an address as default
const setDefaultAddress = (userId, addressId, result) => {
    // First reset all addresses to non-default
    db.query('UPDATE user_address SET is_default = 0 WHERE user_id = ?', [userId], (err) => {
        if (err) {
            console.log(err);
            result(err, null);
            return;
        }
        
        // Now set the specified address as default
        db.query('UPDATE user_address SET is_default = 1 WHERE id = ?', [addressId], (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        });
    });
};

module.exports = {
    getUserAddresses,
    getDefaultAddress,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress
};
