// import express
import express from "express";
// import functions from controller
import {
    showFoods,
    showFoodById,
    createFood,
    updateFood,
    deleteFood,
    showFoodCategories,
    showFoodTypes,
    updateAvailability
} from "../controllers/food.js";

import {
    showAUser,
    createAccount,
    getProfile,
    updateProfile,
    getAddresses,
    addAddress,
    updateAddress,
    deleteAddress,
    getOrders,
    getOrder
} from "../controllers/user.js";

import {
    addItems,
    getItem,
    updateItem,
    allItems,
    deleteItem,
    deleteItems
} from "../controllers/cart.js";

import {
    createBooking
} from "../controllers/booktable.js";

import {
    createBillDetails,getBillDetailsById
} from "../controllers/billdetails.js";

import {
    showNewestStatusId,
    createBillStatus,
    getAllBillsByUser,
    getAllBillsByBill,
    getAllBills,
    updateBillStatus,
    updateBillPaid,
    cancelBillStatus
} from "../controllers/billstatus.js";

import {
    adminLogin,
    checkAdmin,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    updateUserRole
} from "../controllers/admin.js";

import {
    uploadFoodImage,
    uploadMiddleware
} from "../controllers/upload.js";

import {
    getReviewsByFood,
    getUserReviewForFood,
    createReview,
    updateReview,
    deleteReview,
    reportReview,
    getUserReviews,
    getReportedReviews,
    getReviewStats,
    getAllReviewsForAdmin,
    addAdminResponse,
    updateReviewStatus
} from "../controllers/ReviewController.js";

// init express router
const router = express.Router();

////////////////////////// FOOD ////////////////////////////////
// get all Food
router.get("/api/foods", showFoods);

// get single Food
router.get("/api/foods/:id", showFoodById);

// create Food
router.post("/api/foods", createFood);

// update Food
router.put("/api/foods/:id", updateFood);

// delete Food
router.delete("/api/foods/:id", deleteFood);

// get all food categories
router.get("/api/foods/categories/all", showFoodCategories);

// get all food types
router.get("/api/foods/types/all", showFoodTypes);

// update food availability
router.put("/api/foods/:id/availability", updateAvailability);


////////////////////////// IMAGE UPLOADS ////////////////////////////////
// Upload food image
router.post("/api/upload/food-image", uploadMiddleware, uploadFoodImage);


////////////////////////// USER ////////////////////////////////
// get all user
router.get("/api/users/:email", showAUser);

// create account
router.post("/api/users/", createAccount);

// User profile routes
router.get("/api/users/profile/:id", getProfile);
router.put("/api/users/profile/:id", updateProfile);

// User addresses routes
router.get("/api/users/:id/addresses", getAddresses);
router.post("/api/users/:id/addresses", addAddress);
router.put("/api/users/:userId/addresses/:addressId", updateAddress);
router.delete("/api/users/:userId/addresses/:addressId", deleteAddress);

// User orders routes
router.get("/api/users/:id/orders", getOrders);
router.get("/api/users/:userId/orders/:orderId", getOrder);


////////////////////////// ADMIN ////////////////////////////////
// Admin authentication
router.post("/api/admin/login", adminLogin);
router.get("/api/admin/check", checkAdmin);

// Admin user management
router.get("/api/admin/users", getAllUsers);
router.post("/api/admin/users", createUser);
router.put("/api/admin/users/:id", updateUser);
router.delete("/api/admin/users/:id", deleteUser);
router.put("/api/admin/users/:id/toggle-status", toggleUserStatus);
router.put("/api/admin/users/:id/update-role", updateUserRole);


////////////////////////// CART ////////////////////////////////
// add to cart
router.post("/api/cartItem", addItems);

// get a item in cart
router.get("/api/cartItem/:user_id/:food_id", getItem);

// get all items by user id
router.get("/api/cartItem/:id", allItems);

// update item qty
router.put("/api/cartItem/", updateItem);

// delete a item in cart
router.delete("/api/cartItem/:user_id/:food_id", deleteItem);

// delete all items in cart
router.delete("/api/cartItem/:id", deleteItems);



////////////////////////// Booking ////////////////////////////////
router.post("/api/booking", createBooking);



////////////////////////// Bill Details ////////////////////////////////
router.post("/api/billdetails", createBillDetails);
router.get("/api/billdetails/:id", getBillDetailsById);



////////////////////////// Bill Status ////////////////////////////////
router.get("/api/billstatus/new", showNewestStatusId);
router.post("/api/billstatus", createBillStatus);
router.get("/api/billstatus/user/:id", getAllBillsByUser);
router.get("/api/billstatus/bill/:id", getAllBillsByBill);
router.get("/api/billstatus", getAllBills);
router.put("/api/billstatus/:id", updateBillStatus);
router.put("/api/billstatus/paid/:id", updateBillPaid);
router.put("/api/billstatus/cancel/:id", cancelBillStatus);

////////////////////////// Reviews ////////////////////////////////
// Get all reviews for a food item
router.get("/api/foods/:foodId/reviews", getReviewsByFood);
// Get user's review for a specific food item
router.get("/api/users/:userId/foods/:foodId/review", getUserReviewForFood);
// Add a new review
router.post("/api/reviews", createReview);
// Update a review
router.put("/api/reviews/:reviewId", updateReview);
// Delete a review
router.delete("/api/reviews/:reviewId", deleteReview);
// Report a review
router.post("/api/reviews/:reviewId/report", reportReview);
// Get all reviews by a user
router.get("/api/users/:userId/reviews", getUserReviews);
// Get all reported reviews (admin only)
router.get("/api/admin/reviews/reported", getReportedReviews);
// Get review statistics for admin dashboard
router.get("/api/admin/reviews/stats", getReviewStats);
// Get all reviews for admin management with pagination
router.get("/api/admin/reviews", getAllReviewsForAdmin);
// Add admin response to review
router.post("/api/admin/reviews/:reviewId/response", addAdminResponse);
// Update review status (approve/hide/delete)
router.put("/api/admin/reviews/:reviewId/status", updateReviewStatus);

// export default router
export default router;
