<template>
  <!-- Header Section -->
  <div class="md-admin-orders-management">
    <div class="md-admin-header">
      <div class="md-admin-header__title-group">
        <h1 class="md-admin-header__title">
          <i class="material-icons md-admin-header__icon">receipt_long</i>
          Order Management
        </h1>
        <p class="md-admin-header__subtitle">Manage, filter, and resolve customer orders</p>
      </div>

      <div class="md-admin-header__actions">
        <button class="md-button md-button--text" @click="navigateToDashboard">
          <i class="material-icons">dashboard</i>
          <span>Dashboard</span>
        </button>

        <button class="md-button md-button--text" @click="refreshOrders">
          <i class="material-icons">refresh</i>
          <span>Refresh</span>
        </button>

        <button class="md-button md-button--outlined md-button--warn" @click="handleLogout()">
          <i class="material-icons">logout</i>
          <span>Logout</span>
        </button>
      </div>
    </div>

    <!-- Filter Section -->
    <div class="md-admin-filters">
      <div class="md-admin-filter-group">
        <label class="md-admin-filter-label">Filter by Status:</label>
        <div class="md-select-container">
          <select v-model="statusFilter" class="md-select">
            <option value="all">All Status</option>
            <option value="0">Cancelled</option>
            <option value="1">Confirmed</option>
            <option value="2">Preparing</option>
            <option value="3">Checking</option>
            <option value="4">Delivering</option>
            <option value="5">Delivered</option>
            <option value="6">Completed</option>
          </select>
        </div>
      </div>

      <div class="md-admin-filter-group">
        <label class="md-admin-filter-label">Search:</label>
        <div class="md-form-field__input-container">
          <i class="material-icons md-form-field__icon">search</i>
          <input
            type="text"
            class="md-form-field__input"
            placeholder="Search by order ID or customer"
            v-model="searchQuery" />
        </div>
      </div>
    </div>

    <!-- Orders Table -->
    <div class="md-card md-admin-orders">
      <div class="md-admin-orders__header">
        <h2 class="md-admin-orders__title">
          <i class="material-icons">list_alt</i>
          Order List
        </h2>
        <div class="md-admin-orders__stats">
          <span class="md-admin-orders__stat">
            Total: <strong>{{ filteredOrders.length }}</strong>
          </span>
        </div>
      </div>

      <div class="md-admin-orders__content">
        <div class="md-admin-table-container">
          <table class="md-admin-table">
            <thead>
              <tr>
                <th>Order ID</th>
                <th>Customer</th>
                <th>Date</th>
                <th>Total</th>
                <th>Status</th>
                <th>Paid</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="order in paginatedOrders" :key="order.bill_id">
                <td><span class="md-order-id">#{{ order.bill_id }}</span></td>
                <td>{{ order.user_id }}</td>
                <td>{{ formatDate(order.bill_when) }}</td>
                <td><span class="md-order-price">${{ parseFloat(order.bill_total || 0).toFixed(2) }}</span></td>
                <td>
                  <span :class="getStatusClass(order.bill_status)">
                    {{ getStatusLabel(order.bill_status) }}
                  </span>
                </td>
                <td>
                  <span :class="[order.bill_paid === 'true' ? 'md-status--paid' : 'md-status--unpaid']">
                    {{ order.bill_paid === 'true' ? 'Paid' : 'Unpaid' }}
                  </span>
                </td>
                <td class="md-admin-table__actions">
                  <button class="md-button md-button--icon" @click="viewOrderDetails(order.bill_id)" title="View Details">
                    <i class="material-icons">visibility</i>
                  </button>
                  <button class="md-button md-button--icon md-button--success"
                    @click="updateOrderStatus(order.bill_id)"
                    title="Next Status"
                    :disabled="order.bill_status === 6 || order.bill_status === 0">
                    <i class="material-icons">arrow_forward</i>
                  </button>
                  <button class="md-button md-button--icon md-button--info"
                    @click="markAsPaid(order.bill_id)"
                    title="Mark as Paid"
                    :disabled="order.bill_paid === 'true'">
                    <i class="material-icons">payments</i>
                  </button>
                  <button class="md-button md-button--icon md-button--danger"
                    @click="cancelOrder(order.bill_id)"
                    title="Cancel Order"
                    :disabled="order.bill_status === 0 || order.bill_status === 6">
                    <i class="material-icons">cancel</i>
                  </button>
                </td>
              </tr>
              <tr v-if="paginatedOrders.length === 0">
                <td colspan="7" class="md-admin-table__empty">
                  <i class="material-icons">receipt_long</i>
                  <p>No orders found</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="md-admin-pagination">
          <button class="md-button md-button--icon"
            @click="previousPage"
            :disabled="currentPage === 1">
            <i class="material-icons">navigate_before</i>
          </button>
          <span class="md-admin-pagination__info">Page {{ currentPage }} of {{ totalPages }}</span>
          <button class="md-button md-button--icon"
            @click="nextPage"
            :disabled="currentPage === totalPages">
            <i class="material-icons">navigate_next</i>
          </button>
        </div>
      </div>
    </div>

    <!-- Order Details Modal -->
    <div class="md-modal" v-if="selectedOrder">
      <div class="md-modal__backdrop" @click="closeOrderDetails"></div>
      <div class="md-modal__container">
        <div class="md-modal__header">
          <h3 class="md-modal__title">
            <i class="material-icons">receipt_long</i>
            Order #{{ selectedOrder.bill_id }} Details
          </h3>
          <button class="md-button md-button--icon" @click="closeOrderDetails">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="md-modal__content">
          <div v-if="loading" class="md-loading">
            <div class="md-loading__spinner"></div>
            <p>Loading order details...</p>
          </div>

          <div v-else-if="orderItems.length === 0" class="md-empty-state">
            <i class="material-icons">info</i>
            <p>No items found for this order.</p>
          </div>

          <div v-else class="md-order-details">
            <div class="md-order-info">
              <div class="md-order-info__group">
                <h4 class="md-order-info__title">Order Information</h4>
                <div class="md-order-info__row">
                  <span class="md-order-info__label">Status:</span>
                  <span :class="getStatusClass(selectedOrder.bill_status)">
                    {{ getStatusLabel(selectedOrder.bill_status) }}
                  </span>
                </div>
                <div class="md-order-info__row">
                  <span class="md-order-info__label">Payment Status:</span>
                  <span :class="[selectedOrder.bill_paid === 'true' ? 'md-status--paid' : 'md-status--unpaid']">
                    {{ selectedOrder.bill_paid === 'true' ? 'Paid' : 'Unpaid' }}
                  </span>
                </div>
                <div class="md-order-info__row">
                  <span class="md-order-info__label">Date:</span>
                  <span>{{ formatDate(selectedOrder.bill_when) }}</span>
                </div>
                <div class="md-order-info__row">
                  <span class="md-order-info__label">Total:</span>
                  <span class="md-order-price">${{ parseFloat(selectedOrder.bill_total || 0).toFixed(2) }}</span>
                </div>
              </div>

              <div class="md-order-info__group">
                <h4 class="md-order-info__title">Customer Information</h4>
                <div class="md-order-info__row">
                  <span class="md-order-info__label">User ID:</span>
                  <span>{{ selectedOrder.user_id }}</span>
                </div>
              </div>
            </div>

            <h4 class="md-order-items__title">Order Items</h4>
            <div class="md-order-items">
              <div class="md-order-item" v-for="item in orderItems" :key="item.id">
                <div class="md-order-item__image">
                  <img :src="getImagePath(item)" :alt="item.food_title || 'Food Image'">
                </div>
                <div class="md-order-item__details">
                  <h5 class="md-order-item__title">{{ item.food_title || 'Unknown Item' }}</h5>
                  <p class="md-order-item__price">${{ parseFloat(item.price || 0).toFixed(2) }} x {{ parseInt(item.quantity || 0) }}</p>
                </div>
                <div class="md-order-item__total">
                  ${{ (parseFloat(item.price || 0) * parseInt(item.quantity || 0)).toFixed(2) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="md-modal__footer">
          <button class="md-button md-button--text" @click="closeOrderDetails">
            Close
          </button>
          <button class="md-button md-button--success"
            @click="updateOrderStatus(selectedOrder.bill_id)"
            :disabled="selectedOrder.bill_status === 6 || selectedOrder.bill_status === 0">
            <i class="material-icons">arrow_forward</i>
            <span>Update Status</span>
          </button>
          <button class="md-button md-button--info"
            @click="markAsPaid(selectedOrder.bill_id)"
            :disabled="selectedOrder.bill_paid === 'true'">
            <i class="material-icons">payments</i>
            <span>Mark as Paid</span>
          </button>
          <button class="md-button md-button--danger"
            @click="cancelOrder(selectedOrder.bill_id)"
            :disabled="selectedOrder.bill_status === 0 || selectedOrder.bill_status === 6">
            <i class="material-icons">cancel</i>
            <span>Cancel Order</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Dispute Resolution Modal -->
    <div class="md-modal" v-if="showDisputeModal">
      <div class="md-modal__backdrop" @click="closeDisputeModal"></div>
      <div class="md-modal__container">
        <div class="md-modal__header">
          <h3 class="md-modal__title">
            <i class="material-icons">gavel</i>
            Resolve Dispute for Order #{{ disputeOrderId }}
          </h3>
          <button class="md-button md-button--icon" @click="closeDisputeModal">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="md-modal__content">
          <div class="md-form-field">
            <label class="md-form-field__label">Resolution Type</label>
            <div class="md-select-container">
              <select v-model="disputeResolution" class="md-select">
                <option value="refund">Issue Refund</option>
                <option value="replacement">Offer Replacement</option>
                <option value="partial">Partial Refund</option>
                <option value="deny">Deny Claim</option>
              </select>
            </div>
          </div>

          <div class="md-form-field" v-if="disputeResolution === 'partial'">
            <label class="md-form-field__label">Refund Amount ($)</label>
            <div class="md-form-field__input-container">
              <input type="number" step="0.01" min="0" class="md-form-field__input" v-model="refundAmount">
            </div>
          </div>

          <div class="md-form-field">
            <label class="md-form-field__label">Notes</label>
            <div class="md-form-field__input-container">
              <textarea class="md-form-field__textarea" v-model="disputeNotes" rows="4"></textarea>
            </div>
          </div>
        </div>

        <div class="md-modal__footer">
          <button class="md-button md-button--text" @click="closeDisputeModal">
            Cancel
          </button>
          <button class="md-button md-button--primary" @click="resolveDispute">
            <i class="material-icons">check_circle</i>
            <span>Resolve Dispute</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapState, mapMutations } from 'vuex';

export default {
  name: 'OrderManagement',

  data() {
    return {
      allOrders: [],
      statusFilter: 'all',
      searchQuery: '',
      currentPage: 1,
      itemsPerPage: 10,
      selectedOrder: null,
      orderItems: [],
      loading: false,
      showDisputeModal: false,
      disputeOrderId: null,
      disputeResolution: 'refund',
      refundAmount: 0,
      disputeNotes: '',
      statusLabels: {
        0: 'Cancelled',
        1: 'Confirmed',
        2: 'Preparing',
        3: 'Checking',
        4: 'Delivering',
        5: 'Delivered',
        6: 'Completed'
      }
    };
  },

  computed: {
    ...mapState(['admin']),

    filteredOrders() {
      let filtered = this.allOrders;

      // Filter by status
      if (this.statusFilter !== 'all') {
        filtered = filtered.filter(order => order.bill_status.toString() === this.statusFilter);
      }

      // Filter by search query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(order =>
          order.bill_id.toString().includes(query) ||
          order.user_id.toString().includes(query)
        );
      }

      return filtered;
    },

    paginatedOrders() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredOrders.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.filteredOrders.length / this.itemsPerPage) || 1;
    }
  },

  mounted() {
    // Check if admin is logged in
    if (!this.admin || this.admin.type !== 'admin') {
      console.log('Not authenticated as admin, redirecting to admin login');
      this.$router.push("/admin");
      return;
    }

    this.fetchOrders();
  },

  methods: {
    ...mapMutations(['setAdmin']),

    async fetchOrders() {
      try {
        const response = await axios.get('/api/billstatus');
        this.allOrders = response.data;

        // Debug logging to check data structure
        console.log('Fetched orders:', this.allOrders);
        if (this.allOrders.length > 0) {
          console.log('Sample order data:', this.allOrders[0]);
          console.log('Date field (bill_when):', this.allOrders[0].bill_when);
          console.log('Total field (bill_total):', this.allOrders[0].bill_total);
        }
      } catch (error) {
        console.error('Error fetching orders:', error);
      }
    },

    async viewOrderDetails(orderId) {
      this.loading = true;
      this.selectedOrder = this.allOrders.find(order => order.bill_id === orderId);

      try {
        // Get order details (bill items)
        const billDetailsResponse = await axios.get(`/api/billdetails/${orderId}`);
        let orderItems = billDetailsResponse.data;

        // Debug logging for order items
        console.log('Raw bill details response:', orderItems);

        // Check if we already have food details from JOIN query
        if (orderItems.length > 0 && orderItems[0].food_title) {
          console.log('Order items already have food details from JOIN query');
          this.orderItems = orderItems.map(item => ({
            ...item,
            quantity: item.item_qty || 0
          }));
          console.log('Final processed order items:', this.orderItems);
          return;
        }

        // Fetch food data for all items if not already included
        console.log('Fetching food details for all items');
        const enhancedItems = await Promise.all(orderItems.map(async (item) => {
          try {
            // If food_id exists, fetch food details
            if (item.food_id) {
              const foodResponse = await axios.get(`/api/foods/${item.food_id}`);
              const foodData = foodResponse.data;
              console.log(`Food data for ID ${item.food_id}:`, foodData);

              return {
                ...item,
                food_title: foodData.food_name,
                price: foodData.food_price,
                food_image: foodData.food_image,
                food_description: foodData.food_desc,
                quantity: item.item_qty || 0
              };
            }
            return {
              ...item,
              quantity: item.item_qty || 0
            };
          } catch (error) {
            console.error(`Error fetching food details for food_id ${item.food_id}:`, error);
            return {
              ...item,
              food_title: `Food #${item.food_id}`,
              price: 0,
              quantity: item.item_qty || 0
            };
          }
        }));

        this.orderItems = enhancedItems;
        console.log('Final processed order items:', this.orderItems);
      } catch (error) {
        console.error('Error fetching order details:', error);
        this.orderItems = [];
      } finally {
        this.loading = false;
      }
    },

    closeOrderDetails() {
      this.selectedOrder = null;
      this.orderItems = [];
    },

    async updateOrderStatus(orderId) {
      try {
        await axios.put(`/api/billstatus/${orderId}`);
        await this.fetchOrders();

        // If details modal is open, update selected order
        if (this.selectedOrder && this.selectedOrder.bill_id === orderId) {
          this.selectedOrder = this.allOrders.find(order => order.bill_id === orderId);
        }
      } catch (error) {
        console.error('Error updating order status:', error);
      }
    },

    async markAsPaid(orderId) {
      try {
        await axios.put(`/api/billstatus/paid/${orderId}`);
        await this.fetchOrders();

        // If details modal is open, update selected order
        if (this.selectedOrder && this.selectedOrder.bill_id === orderId) {
          this.selectedOrder = this.allOrders.find(order => order.bill_id === orderId);
        }
      } catch (error) {
        console.error('Error marking order as paid:', error);
      }
    },

    async cancelOrder(orderId) {
      try {
        await axios.put(`/api/billstatus/cancel/${orderId}`);
        await this.fetchOrders();

        // If details modal is open, update selected order
        if (this.selectedOrder && this.selectedOrder.bill_id === orderId) {
          this.selectedOrder = this.allOrders.find(order => order.bill_id === orderId);
        }
      } catch (error) {
        console.error('Error cancelling order:', error);
      }
    },

    openDisputeModal(orderId) {
      this.disputeOrderId = orderId;
      this.showDisputeModal = true;
      this.disputeResolution = 'refund';
      this.refundAmount = 0;
      this.disputeNotes = '';
    },

    closeDisputeModal() {
      this.showDisputeModal = false;
      this.disputeOrderId = null;
    },

    resolveDispute() {
      // In a real application, we would send this data to the backend
      console.log('Resolving dispute for order:', this.disputeOrderId);
      console.log('Resolution type:', this.disputeResolution);
      console.log('Refund amount:', this.refundAmount);
      console.log('Notes:', this.disputeNotes);

      // Close modal
      this.closeDisputeModal();
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A';

      try {
        // Handle different date formats
        let date;

        // If it's already a valid date string
        if (dateString instanceof Date) {
          date = dateString;
        } else if (typeof dateString === 'string') {
          // Try parsing as-is first
          date = new Date(dateString);

          // If invalid, try parsing as timestamp
          if (isNaN(date.getTime())) {
            const timestamp = parseInt(dateString);
            if (!isNaN(timestamp)) {
              date = new Date(timestamp);
            }
          }
        } else if (typeof dateString === 'number') {
          // Handle timestamp
          date = new Date(dateString);
        }

        // Check if date is valid
        if (!date || isNaN(date.getTime())) {
          console.warn('Invalid date:', dateString);
          return 'Invalid Date';
        }

        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        }) + ' ' + date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });
      } catch (error) {
        console.error('Error formatting date:', error, dateString);
        return 'N/A';
      }
    },

    getStatusLabel(status) {
      return this.statusLabels[status] || 'Unknown';
    },

    getStatusClass(status) {
      const statusMap = {
        0: 'md-status--cancelled',
        1: 'md-status--confirmed',
        2: 'md-status--preparing',
        3: 'md-status--checking',
        4: 'md-status--delivering',
        5: 'md-status--delivered',
        6: 'md-status--completed'
      };

      return statusMap[status] || 'md-status--unknown';
    },

    getImagePath(item) {
      // First, handle null/undefined data gracefully
      if (!item || !item.food_image) {
        return require('../assets/images/nachos-img.png');
      }

      const imageName = item.food_image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // Handle cases where database returns 'food-img.png' which doesn't exist
      if (imageName === 'food-img.png') {
        return require('../assets/images/nachos-img.png');
      }

      // Use a safer approach with a default fallback image
      if (imageName) {
        try {
          return require(`../assets/images/${imageName}`);
        } catch (error) {
          // Image not found, fallback to default
          console.log('Image not found:', imageName);
          return require('../assets/images/nachos-img.png');
        }
      }

      // Default fallback
      return require('../assets/images/nachos-img.png');
    },

    navigateToDashboard() {
      this.$router.push('/admin/dashboard');
    },

    refreshOrders() {
      this.fetchOrders();
    },

    handleLogout() {
      this.setAdmin(null);
      this.$router.push('/admin');
    },

    previousPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    }
  }
};
</script>

<style scoped>
/* Admin Orders Management Styles */
.md-admin-orders-management {
  padding: var(--md-sys-spacing-medium);
}

/* Header Styles */
.md-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-admin-header__title-group {
  display: flex;
  flex-direction: column;
}

.md-admin-header__title {
  display: flex;
  align-items: center;
  font-size: 1.75rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-admin-header__icon {
  font-size: 1.75rem;
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-admin-header__subtitle {
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface-variant);
  margin: var(--md-sys-spacing-xsmall) 0 0 0;
}

.md-admin-header__actions {
  display: flex;
  gap: var(--md-sys-spacing-small);
}

/* Filters Styles */
.md-admin-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-medium);
  margin-bottom: var(--md-sys-spacing-medium);
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-medium);
}

.md-admin-filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.md-admin-filter-label {
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: var(--md-sys-spacing-xsmall);
}

.md-select-container {
  position: relative;
}

.md-select {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  appearance: none;
  cursor: pointer;
}

.md-select:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
}

.md-form-field__input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-small);
  overflow: hidden;
}

.md-form-field__icon {
  padding: 0 0.75rem;
  color: var(--md-sys-color-on-surface-variant);
}

.md-form-field__input {
  flex: 1;
  border: none;
  padding: 0.75rem;
  font-size: 1rem;
  background-color: transparent;
  color: var(--md-sys-color-on-surface);
}

.md-form-field__input:focus {
  outline: none;
}

/* Orders Table Styles */
.md-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-medium);
  box-shadow: var(--md-sys-elevation-2);
  overflow: hidden;
  margin-bottom: var(--md-sys-spacing-large);
}

.md-admin-orders__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-admin-orders__title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.md-admin-orders__title i {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-admin-table-container {
  overflow-x: auto;
}

.md-admin-table {
  width: 100%;
  border-collapse: collapse;
}

.md-admin-table th,
.md-admin-table td {
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  text-align: left;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-admin-table th {
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
  background-color: var(--md-sys-color-surface-variant);
  white-space: nowrap;
}

.md-admin-table__actions {
  display: flex;
  gap: var(--md-sys-spacing-xsmall);
}

.md-admin-table__empty {
  padding: var(--md-sys-spacing-xlarge) !important;
  text-align: center !important;
  color: var(--md-sys-color-on-surface-variant);
}

.md-admin-table__empty i {
  font-size: 2rem;
  margin-bottom: var(--md-sys-spacing-small);
  opacity: 0.5;
}

.md-order-id {
  font-weight: 500;
  color: var(--md-sys-color-primary);
}

.md-order-price {
  font-weight: 500;
  color: var(--md-sys-color-primary);
}

/* Status Labels */
.md-status--cancelled,
.md-status--confirmed,
.md-status--preparing,
.md-status--checking,
.md-status--delivering,
.md-status--delivered,
.md-status--completed,
.md-status--paid,
.md-status--unpaid {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.md-status--cancelled {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

.md-status--confirmed {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.md-status--preparing {
  background-color: #fff8e1;
  color: #ff6f00;
}

.md-status--checking {
  background-color: #e3f2fd;
  color: #0277bd;
}

.md-status--delivering {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.md-status--delivered {
  background-color: #e0f7fa;
  color: #006064;
}

.md-status--completed {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.md-status--paid {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.md-status--unpaid {
  background-color: #fbe9e7;
  color: #c62828;
}

/* Pagination Styles */
.md-admin-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--md-sys-spacing-medium);
  gap: var(--md-sys-spacing-medium);
}

.md-admin-pagination__info {
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface-variant);
}

/* Modal Styles */
.md-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.md-modal__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.md-modal__container {
  position: relative;
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-large);
  box-shadow: var(--md-sys-elevation-3);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.md-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-modal__title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.md-modal__title i {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-modal__content {
  padding: var(--md-sys-spacing-medium);
  overflow-y: auto;
  flex: 1;
}

.md-modal__footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-medium);
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

/* Order Details Styles */
.md-order-details {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-order-info {
  display: flex;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-large);
}

.md-order-info__group {
  flex: 1;
  min-width: 200px;
}

.md-order-info__title {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 var(--md-sys-spacing-small) 0;
  color: var(--md-sys-color-on-surface);
}

.md-order-info__row {
  display: flex;
  margin-bottom: var(--md-sys-spacing-xsmall);
}

.md-order-info__label {
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
  margin-right: var(--md-sys-spacing-small);
}

.md-order-items__title {
  font-size: 1rem;
  font-weight: 500;
  margin: var(--md-sys-spacing-medium) 0 var(--md-sys-spacing-small) 0;
  color: var(--md-sys-color-on-surface);
}

.md-order-items {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-small);
}

.md-order-item {
  display: flex;
  align-items: center;
  padding: var(--md-sys-spacing-small);
  border-radius: var(--md-sys-shape-small);
  background-color: var(--md-sys-color-surface-variant);
}

.md-order-item__image {
  width: 50px;
  height: 50px;
  border-radius: var(--md-sys-shape-small);
  overflow: hidden;
  margin-right: var(--md-sys-spacing-small);
}

.md-order-item__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-order-item__details {
  flex: 1;
}

.md-order-item__title {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 var(--md-sys-spacing-xsmall) 0;
  color: var(--md-sys-color-on-surface);
}

.md-order-item__price {
  font-size: 0.75rem;
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.md-order-item__total {
  font-weight: 500;
  color: var(--md-sys-color-primary);
}

/* Loading State */
.md-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-xlarge);
  color: var(--md-sys-color-on-surface-variant);
}

.md-loading__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--md-sys-color-surface-variant);
  border-top-color: var(--md-sys-color-primary);
  border-radius: 50%;
  animation: spinner 1s infinite linear;
  margin-bottom: var(--md-sys-spacing-medium);
}

@keyframes spinner {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Empty State */
.md-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-xlarge);
  color: var(--md-sys-color-on-surface-variant);
}

.md-empty-state i {
  font-size: 2rem;
  margin-bottom: var(--md-sys-spacing-small);
  opacity: 0.5;
}

/* Buttons */
.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--md-sys-shape-small);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
}

.md-button--icon {
  padding: 0.5rem;
  border-radius: 50%;
}

.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.md-button--outlined {
  background-color: transparent;
  border: 1px solid currentColor;
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button--success {
  color: #2e7d32;
}

.md-button--info {
  color: #0277bd;
}

.md-button--warn {
  color: var(--md-sys-color-error);
}

.md-button--danger {
  color: var(--md-sys-color-error);
}

.md-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.md-button--primary:hover {
  background-color: var(--md-sys-color-primary-hover, var(--md-sys-color-primary));
}

.md-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form field textarea */
.md-form-field__textarea {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  resize: vertical;
}

.md-form-field__textarea:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .md-admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--md-sys-spacing-medium);
  }

  .md-admin-header__actions {
    width: 100%;
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--md-sys-spacing-xsmall);
  }

  .md-admin-filter-group {
    min-width: 100%;
  }

  .md-order-info {
    flex-direction: column;
    gap: var(--md-sys-spacing-medium);
  }
}
</style>
