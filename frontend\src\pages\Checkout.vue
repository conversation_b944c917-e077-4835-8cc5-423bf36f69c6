<template>
  <div class="md-checkout">
    <div class="md-section-header">
      <h1 class="md-section-header__title">Checkout</h1>
      <p class="md-section-header__subtitle">Complete your order details</p>
    </div>

    <div class="md-checkout__container">
      <form id="checkoutForm" @submit.prevent="handleSubmit" novalidate autocomplete="off" class="md-checkout__form">
        <div class="md-checkout__content">
          <!-- Order summary section -->
          <div class="md-checkout__section md-checkout__summary">
            <h2 class="md-checkout__section-title">Order Summary</h2>

            <!-- Cart Items Display -->
            <div class="md-cart-items-summary">
              <h3 class="md-cart-items-summary__title">Your Items</h3>
              <div class="md-cart-items-summary__list">
                <div v-for="(item, index) in cartDetails" :key="index" class="md-cart-item-summary">
                  <div class="md-cart-item-summary__info">
                    <span class="md-cart-item-summary__name">{{ item.food_name }}</span>
                    <span class="md-cart-item-summary__desc">{{ item.food_desc }}</span>
                    <span class="md-cart-item-summary__qty">Qty: {{ item.item_qty }}</span>
                  </div>
                  <div class="md-cart-item-summary__price">
                    <span>${{ ((parseFloat(item.food_price || 0) - parseFloat(item.food_discount || 0)) * parseInt(item.item_qty || 1)).toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="md-summary-card">
              <div class="md-summary-card__row">
                <span class="md-summary-card__label">Subtotal</span>
                <span class="md-summary-card__value">${{ calculateSummaryPrice()[0] }}</span>
              </div>
              <div class="md-summary-card__row">
                <span class="md-summary-card__label">Discount</span>
                <span class="md-summary-card__value">-${{ calculateSummaryPrice()[1] }}</span>
              </div>
              <div class="md-summary-card__row">
                <span class="md-summary-card__label">Delivery</span>
                <span class="md-summary-card__value">${{ calculateSummaryPrice()[2] }}</span>
              </div>
              <div class="md-summary-card__row md-summary-card__total">
                <span class="md-summary-card__label">Total</span>
                <span class="md-summary-card__value">${{ calculateSummaryPrice()[3] }}</span>
              </div>
            </div>
          </div>

          <!-- Shipping details section -->
          <div class="md-checkout__section">
            <h2 class="md-checkout__section-title">Shipping Details</h2>

            <!-- Phone Number Selection -->
            <div v-if="user && user.user_phone" class="md-form-field md-form-field--phone">
              <div class="md-form-field__header">
                <label class="md-form-field__label">Phone Number</label>
                <button
                  type="button"
                  class="md-button md-button--text md-button--small"
                  @click="useNewPhone = !useNewPhone"
                >
                  {{ useNewPhone ? 'Use Existing' : 'Use New' }}
                </button>
              </div>

              <div v-if="!useNewPhone" class="md-existing-phone">
                <div class="md-existing-phone__content">
                  <span class="md-existing-phone__value">{{ user.user_phone }}</span>
                </div>
              </div>

              <input
                v-else
                type="tel"
                id="coPhone"
                class="md-form-field__input"
                v-model="checkoutObj.phone"
                placeholder="Enter your phone number"
              />
            </div>
            <div v-else class="md-form-field">
              <label for="coPhone" class="md-form-field__label">Phone Number</label>
              <input
                type="tel"
                id="coPhone"
                class="md-form-field__input"
                v-model="checkoutObj.phone"
                placeholder="Enter your phone number"
              />
              <p class="md-form-field__error" v-if="errorObj.phoneErr.length > 0">{{ errorObj.phoneErr[0] }}</p>
            </div>

            <!-- Address Selection -->
            <div v-if="userAddresses.length > 0" class="md-form-field">
              <div class="md-form-field__header">
                <label class="md-form-field__label">Select Delivery Address</label>
                <button
                  type="button"
                  class="md-button md-button--text md-button--small"
                  @click="showAddressForm = !showAddressForm; selectedAddressId = showAddressForm ? 'new' : ''"
                >
                  {{ showAddressForm ? 'Use Existing' : 'Add New' }}
                </button>
              </div>

              <div v-if="!showAddressForm" class="md-select md-address-select">
                <select v-model="selectedAddressId" class="md-select__input" @change="handleAddressSelection">
                  <option value="">-- Select an address --</option>
                  <option v-for="address in userAddresses" :key="address.id" :value="address.id">
                    {{ address.label || `${address.street}, ${address.city}` }}
                    {{ address.note ? ` - ${address.note}` : '' }}
                    {{ address.isDefault ? ' (Default)' : '' }}
                  </option>
                </select>
                <span class="material-icons-round md-select__icon">expand_more</span>
              </div>
            </div>

            <!-- New Address Form -->
            <div v-if="showAddressForm || userAddresses.length === 0" class="md-address-form-container">
              <h3 class="md-address-form__title">{{ userAddresses.length === 0 ? 'Add Delivery Address' : 'New Address' }}</h3>

              <div class="md-form-field">
                <label for="addressLabel" class="md-form-field__label">Address Label</label>
                <select
                  id="addressLabel"
                  class="md-form-field__input"
                  v-model="newAddress.address_label"
                >
                  <option value="Home">🏠 Home</option>
                  <option value="Work">🏢 Work</option>
                  <option value="Other">📍 Other</option>
                </select>
                <p class="md-form-field__helper">Choose a label for this address</p>
              </div>

              <div class="md-form-grid">
                <div class="md-form-field">
                  <label for="recipientName" class="md-form-field__label">Recipient Name *</label>
                  <input
                    type="text"
                    id="recipientName"
                    class="md-form-field__input"
                    v-model="newAddress.recipient_name"
                    placeholder="Full name"
                  />
                  <p class="md-form-field__error" v-if="addressErrors.recipient_name">{{ addressErrors.recipient_name }}</p>
                </div>

                <div class="md-form-field">
                  <label for="recipientPhone" class="md-form-field__label">Phone Number *</label>
                  <input
                    type="tel"
                    id="recipientPhone"
                    class="md-form-field__input"
                    v-model="newAddress.recipient_phone"
                    placeholder="0123456789"
                  />
                  <p class="md-form-field__error" v-if="addressErrors.recipient_phone">{{ addressErrors.recipient_phone }}</p>
                </div>
              </div>

              <div class="md-form-field">
                <label for="addressLine1" class="md-form-field__label">Street Address *</label>
                <input
                  type="text"
                  id="addressLine1"
                  class="md-form-field__input"
                  v-model="newAddress.address_line1"
                  placeholder="House number, street name"
                />
                <p class="md-form-field__error" v-if="addressErrors.address_line1">{{ addressErrors.address_line1 }}</p>
              </div>

              <div class="md-form-field">
                <label for="addressLine2" class="md-form-field__label">Ward/Commune</label>
                <input
                  type="text"
                  id="addressLine2"
                  class="md-form-field__input"
                  v-model="newAddress.address_line2"
                  placeholder="Ward, commune (optional)"
                />
              </div>

              <div class="md-form-grid">
                <div class="md-form-field">
                  <label for="district" class="md-form-field__label">District</label>
                  <input
                    type="text"
                    id="district"
                    class="md-form-field__input"
                    v-model="newAddress.district"
                    placeholder="District"
                  />
                </div>

                <div class="md-form-field">
                  <label for="addressCity" class="md-form-field__label">City/Province *</label>
                  <input
                    type="text"
                    id="addressCity"
                    class="md-form-field__input"
                    v-model="newAddress.city"
                    placeholder="City or province"
                  />
                  <p class="md-form-field__error" v-if="addressErrors.city">{{ addressErrors.city }}</p>
                </div>
              </div>

              <div class="md-form-grid">
                <div class="md-form-field">
                  <label for="postalCode" class="md-form-field__label">Postal Code</label>
                  <input
                    type="text"
                    id="postalCode"
                    class="md-form-field__input"
                    v-model="newAddress.postal_code"
                    placeholder="Postal code (optional)"
                  />
                </div>

                <div class="md-form-field">
                  <label for="country" class="md-form-field__label">Country</label>
                  <input
                    type="text"
                    id="country"
                    class="md-form-field__input"
                    v-model="newAddress.country"
                    placeholder="Vietnam"
                  />
                </div>
              </div>

              <div class="md-checkbox">
                <input type="checkbox" id="defaultAddress" v-model="newAddress.is_default" />
                <label for="defaultAddress">Set as default address</label>
              </div>

              <div v-if="userAddresses.length > 0" class="md-form-actions">
                <button type="button" class="md-button md-button--text" @click="cancelNewAddress">Cancel</button>
                <button type="button" class="md-button md-button--filled" @click="saveNewAddress">Save Address</button>
              </div>
            </div>

            <!-- Selected Address Display -->
            <div v-if="selectedAddress && !showAddressForm" class="md-selected-address">
              <div class="md-selected-address__content">
                <h3 class="md-selected-address__label">{{ selectedAddress.label || 'Delivery Address' }}</h3>
                <p class="md-selected-address__text">{{ selectedAddress.street }}</p>
                <p class="md-selected-address__text">{{ selectedAddress.city }}</p>
                <p v-if="selectedAddress.note" class="md-selected-address__note">Note: {{ selectedAddress.note }}</p>
              </div>
              <button type="button" class="md-button md-button--text" @click="changeAddress">
                <span class="material-icons-round">edit</span>
                Change
              </button>
            </div>

            <!-- Hidden address field for form submission -->
            <input type="hidden" v-model="checkoutObj.address" />
          </div>

          <!-- Payment method section -->
          <div class="md-checkout__section">
            <h2 class="md-checkout__section-title">Payment Method</h2>
            <div class="md-radio-group">
              <div class="md-radio">
                <input type="radio" id="paymentCash" value="cash" v-model="checkoutObj.paymentMethod" />
                <label for="paymentCash">
                  <i class="material-icons">payments</i>
                  Cash on Delivery
                </label>
              </div>
              <div class="md-radio">
                <input type="radio" id="paymentCard" value="card" v-model="checkoutObj.paymentMethod" />
                <label for="paymentCard">
                  <i class="material-icons">credit_card</i>
                  Credit Card
                </label>
              </div>
              <p class="md-form-field__error" v-if="errorObj.payErr.length > 0">{{ errorObj.payErr[0] }}</p>
            </div>

            <!-- Card payment details -->
            <div v-if="checkoutObj.paymentMethod === 'card'" class="md-checkout__card-details">
              <div class="md-form-field">
                <label for="coCardNum" class="md-form-field__label">Card Number</label>
                <input
                  type="text"
                  id="coCardNum"
                  class="md-form-field__input"
                  v-model="cardObj.number"
                  placeholder="Enter your card number"
                  maxlength="16"
                />
                <p class="md-form-field__error" v-if="errorObj.numErr.length > 0">{{ errorObj.numErr[0] }}</p>
              </div>

              <div class="md-form-field">
                <label for="coCardName" class="md-form-field__label">Cardholder Name</label>
                <input
                  v-upcase
                  type="text"
                  id="coCardName"
                  class="md-form-field__input"
                  v-model="cardObj.name"
                  placeholder="Enter the name on your card"
                />
                <p class="md-form-field__error" v-if="errorObj.nameErr.length > 0">{{ errorObj.nameErr[0] }}</p>
              </div>

              <div class="md-form-grid">
                <div class="md-form-field">
                  <label for="coCardEx" class="md-form-field__label">Expiration Date</label>
                  <input
                    type="month"
                    id="coCardEx"
                    class="md-form-field__input"
                    v-model="cardObj.expiryDate"
                    @click="availableTime()"
                  />
                  <p class="md-form-field__error" v-if="errorObj.exDateErr.length > 0">{{ errorObj.exDateErr[0] }}</p>
                </div>

                <div class="md-form-field">
                  <label for="coCardCvv" class="md-form-field__label">CVV</label>
                  <input
                    type="text"
                    id="coCardCvv"
                    class="md-form-field__input"
                    v-model="cardObj.cvv"
                    placeholder="CVV"
                    maxlength="3"
                  />
                  <p class="md-form-field__error" v-if="errorObj.cvvErr.length > 0">{{ errorObj.cvvErr[0] }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="md-checkout__actions">
          <button
            type="button"
            class="md-button md-button--outline"
            @click="$router.push('/cart')"
          >
            <i class="material-icons">arrow_back</i>
            Back to Cart
          </button>

          <button
            type="submit"
            class="md-button md-button--primary"
            :disabled="!cartDetails.length || isLoading"
          >
            <i class="material-icons">payment</i>
            Place Order
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import apiService from "../services/ApiService";

export default {
  name: "CheckoutPage",
  directives: {
    upcase: {
      mounted(el) {
        el.style.textTransform = "uppercase";
      }
    }
  },

  data() {
    return {
      cartItem: [],
      itemQuantity: [],
      cartDetails: [],
      userAddresses: [],
      selectedAddressId: '',
      selectedAddress: null,
      showAddressForm: false,
      useNewPhone: false,
      // User data from local storage or session
      user: JSON.parse(localStorage.getItem('user')) || null,
      // Cart items for display
      filterFoods: [],
      newAddress: {
        address_label: 'Home',
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        address_line2: '',
        district: '',
        city: '',
        postal_code: '',
        country: 'Vietnam',
        is_default: false
      },
      addressErrors: {
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        city: ''
      },
      checkoutObj: {
        phone: "",
        address: "",
        paymentMethod: "cash",
      },
      cardObj: {
        number: "",
        name: "",
        expiryDate: "",
        cvv: "",
      },
      errorObj: {
        phoneErr: [],
        addressErr: [],
        numErr: [],
        nameErr: [],
        exDateErr: [],
        cvvErr: [],
        payErr: []
      },
      isLoading: true,
      cardDetails: {
        number: "",
        name: "",
        expiry: "",
        cvv: ""
      },
      cardErrors: {
        number: "",
        name: "",
        expiry: "",
        cvv: ""
      },
      showCardForm: false
    };
  },

  computed: {
    // Get user from Vuex store
    storeUser() {
      return this.$store.state.user;
    }
  },

  watch: {
    // Watch for changes in store user
    storeUser: {
      immediate: true,
      handler(newUser) {
        if (newUser && newUser.user_id) {
          this.user = newUser;
          console.log('User updated from store:', newUser);

          // Load user addresses and pre-fill phone if available
          this.loadUserAddresses();
          if (newUser.user_phone) {
            this.checkoutObj.phone = newUser.user_phone;
          }
        } else if (!newUser) {
          this.user = null;
          console.log('User cleared from store');
        }
      }
    }
  },

  created() {
    // Check user authentication status
    this.checkAuth();
    // Initialize cart data
    this.initializeCartData();
  },

  mounted() {
    // User-related initialization is now handled by the watcher
    // This ensures proper reactivity when user data changes
    console.log('Checkout component mounted');
  },

  methods: {
    // Check if user is authenticated
    checkAuth() {
      // First check Vuex store
      if (this.$store.state.user) {
        this.user = this.$store.state.user;
        console.log('User data loaded from Vuex store:', this.user);
        return;
      }

      // If not in store, try localStorage
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          this.user = JSON.parse(userData);
          // Also update the store
          this.$store.commit('setUser', this.user);
          console.log('User data loaded from localStorage:', this.user);
          return;
        } catch (e) {
          console.error('Error parsing user data from localStorage:', e);
          localStorage.removeItem('user'); // Clear corrupted data
        }
      }

      // If not in localStorage, try sessionStorage
      const sessionUser = sessionStorage.getItem('user');
      if (sessionUser) {
        try {
          this.user = JSON.parse(sessionUser);
          // Also update the store and localStorage
          this.$store.commit('setUser', this.user);
          console.log('User data loaded from sessionStorage:', this.user);
          return;
        } catch (e) {
          console.error('Error parsing user data from sessionStorage:', e);
          sessionStorage.removeItem('user'); // Clear corrupted data
        }
      }

      // No user found anywhere
      this.user = null;
      console.log('User authentication status: Not logged in');

      // Redirect to login if no user is found
      if (!this.user) {
        console.warn('No authenticated user found, redirecting to login');
        this.$router.push('/login');
      }
    },

    // Debug method to check user authentication state
    debugUserAuth() {
      console.log('=== USER AUTHENTICATION DEBUG ===');
      console.log('this.user:', this.user);
      console.log('$store.state.user:', this.$store.state.user);
      console.log('localStorage user:', localStorage.getItem('user'));
      console.log('sessionStorage user:', sessionStorage.getItem('user'));
      console.log('================================');

      if (this.user) {
        console.log('User ID:', this.user.user_id);
        console.log('User Name:', this.user.user_name);
        console.log('User Email:', this.user.user_email);
      } else {
        console.log('No user data available');
      }
    },

    // Initialize cart data - called during component creation
    initializeCartData() {
      console.log('Initializing cart data...');
      this.getAllCartItem();
    },
    calculateSummaryPrice() {
      // Calculate subtotal (sum of all items)
      let subtotal = 0;
      let discountTotal = 0;

      // Calculate subtotal and discounts from cart items
      if (this.cartDetails && this.cartDetails.length > 0) {
        this.cartDetails.forEach(item => {
          const price = parseFloat(item.food_price) || 0;
          const discount = parseFloat(item.food_discount) || 0;
          const quantity = parseInt(item.item_qty) || 1;

          subtotal += price * quantity;
          discountTotal += discount * quantity;
        });
      }

      // Fixed delivery fee (can be modified if you have different delivery options)
      const deliveryFee = this.cartDetails && this.cartDetails.length > 0 ? 2.99 : 0;

      // Calculate total
      const total = subtotal - discountTotal + deliveryFee;

      // Return array of values formatted to 2 decimal places
      return [
        subtotal.toFixed(2),
        discountTotal.toFixed(2),
        deliveryFee.toFixed(2),
        total.toFixed(2)
      ];
    },

    async getAllCartItem() {
      try {
        this.isLoading = true;
        console.log('Starting to load cart items...');

        // First, check if user is logged in
        if (!this.user || !this.user.user_id) {
          console.warn('No user logged in, attempting to get cart from localStorage');
          await this.loadCartFromLocalStorage();
          return;
        }

        console.log('Fetching cart items for user:', this.user.user_id);

        try {
          // Get cart items from API
          const response = await axios.get(`/api/cartItem/${this.user.user_id}`);
          console.log('API cart response:', response.data);

          if (response.data && Array.isArray(response.data) && response.data.length > 0) {
            // Process cart data from API - now includes food details from JOIN
            this.cartDetails = response.data.map(item => ({
              ...item,
              food_price: parseFloat(item.food_price) || 0,
              food_discount: parseFloat(item.food_discount) || 0,
              item_qty: parseInt(item.item_qty) || 1
            }));

            // Debug log to verify data structure
            console.log('Cart data with food details:', this.cartDetails);

            // Make sure number values are properly parsed
            this.cartDetails.forEach(item => {
              // Ensure all required properties are present
              if (!item.food_name) console.warn('Missing food_name in cart item:', item);
              if (isNaN(item.food_price)) console.warn('Invalid food_price in cart item:', item);

              // Ensure numeric values
              item.food_price = parseFloat(item.food_price) || 0;
              item.food_discount = parseFloat(item.food_discount) || 0;
              item.item_qty = parseInt(item.item_qty) || 1;

              // Debug individual item
              console.log(`Item: ${item.food_name}, Price: ${item.food_price}, Qty: ${item.item_qty}, Total: ${item.food_price * item.item_qty}`);
            });

            // Extract basic IDs and quantities for compatibility
            this.cartItem = this.cartDetails.map(item => item.food_id);
            this.itemQuantity = this.cartDetails.map(item => item.item_qty);

            // Update filterFoods for the template
            this.filterFoods = [...this.cartDetails];

            // Save to localStorage as backup
            localStorage.setItem('cartItems', JSON.stringify(this.cartItem));
            localStorage.setItem('itemQuantities', JSON.stringify(this.itemQuantity));
            localStorage.setItem('cartDetails', JSON.stringify(this.cartDetails));

            console.log('Cart items loaded successfully with food details:', this.cartDetails.length, 'items');
          } else {
            console.warn('No cart items found from API, trying localStorage');
            await this.loadCartFromLocalStorage();
          }
        } catch (apiError) {
          console.error('API Error:', apiError);
          // Fallback to localStorage if API fails
          await this.loadCartFromLocalStorage();
        }
      } catch (error) {
        console.error('Error in getAllCartItem:', error);
        await this.loadCartFromLocalStorage();
      } finally {
        this.isLoading = false;
      }
    },

    // Helper method to load cart from localStorage as fallback
    async loadCartFromLocalStorage() {
      try {
        console.log('Loading cart from localStorage...');

        // Try to get cart items from localStorage
        const storedCart = localStorage.getItem('cartItems');
        if (storedCart) {
          try {
            this.cartItem = JSON.parse(storedCart);
            console.log('Loaded cart items from localStorage:', this.cartItem);
          } catch (e) {
            console.error('Error parsing cartItems from localStorage:', e);
            this.cartItem = [];
          }
        } else {
          this.cartItem = [];
        }

        // Try to get quantities
        const storedQuantities = localStorage.getItem('itemQuantities');
        if (storedQuantities) {
          try {
            this.itemQuantity = JSON.parse(storedQuantities);
          } catch (e) {
            console.error('Error parsing itemQuantities from localStorage:', e);
            this.itemQuantity = [];
          }
        } else {
          this.itemQuantity = [];
        }

        // Get detailed cart information
        const storedDetails = localStorage.getItem('cartDetails');
        if (storedDetails) {
          try {
            this.cartDetails = JSON.parse(storedDetails);
            console.log('Loaded cart details from localStorage:', this.cartDetails);
          } catch (e) {
            console.error('Error parsing cartDetails from localStorage:', e);
            this.cartDetails = [];
          }
        } else {
          this.cartDetails = [];
        }

        // If we have items but no details, try to get details from Vuex store
        if (this.cartItem.length > 0 && (!this.cartDetails || this.cartDetails.length === 0)) {
          if (this.$store && this.$store.state && this.$store.state.allFoods) {
            // Filter foods from store that match IDs in cart
            const allFoods = this.$store.state.allFoods;
            this.cartDetails = this.cartItem.map((foodId, index) => {
              const food = allFoods.find(f => f.food_id === foodId);
              return food ? {
                ...food,
                item_qty: this.itemQuantity[index] || 1,
                food_price: parseFloat(food.food_price) || 0,
                food_discount: parseFloat(food.food_discount) || 0
              } : null;
            }).filter(Boolean); // Remove any null items

            console.log('Built cart details from Vuex store:', this.cartDetails);
          }
        }

        // If we still don't have cart details, try to fetch foods from API
        if ((!this.cartDetails || this.cartDetails.length === 0) && this.cartItem.length > 0) {
          await this.fetchFoodsForCart();
          return;
        }

        // Update filterFoods to be a copy of cartDetails
        this.filterFoods = [...(this.cartDetails || [])];

        console.log('Cart loaded from localStorage, items:', this.cartDetails.length);
      } catch (error) {
        console.error('Error loading cart from localStorage:', error);
        // Initialize empty arrays as fallback
        this.cartItem = [];
        this.itemQuantity = [];
        this.cartDetails = [];
        this.filterFoods = [];
      } finally {
        this.isLoading = false;
      }
    },

    // Method to fetch food details from API when we only have IDs
    async fetchFoodsForCart() {
      console.log('Attempting to fetch food details for cart items');

      try {
        if (!this.cartItem || this.cartItem.length === 0) {
          console.warn('No cart item IDs available to fetch details');
          this.isLoading = false;
          return;
        }

        console.log('Fetching details for cart items:', this.cartItem);

        // Build an array of promises for each food ID
        const foodPromises = this.cartItem.map(async (foodId, index) => {
          try {
            if (!foodId) {
              console.warn('Invalid food ID in cart:', foodId);
              return null;
            }

            // Fetch the food details from API
            const response = await axios.get(`/api/foods/${foodId}`);
            if (response.data) {
              const qty = this.itemQuantity[index] || 1;
              console.log(`Found food details for ID ${foodId}, qty: ${qty}`);

              // Return the food with quantity from itemQuantity array
              return {
                ...response.data,
                item_qty: qty,
                food_price: parseFloat(response.data.food_price) || 0,
                food_discount: parseFloat(response.data.food_discount) || 0
              };
            }
            console.warn(`No data returned for food ID ${foodId}`);
            return null;
          } catch (foodError) {
            console.error(`Error fetching details for food ID ${foodId}:`, foodError);
            return null;
          }
        });

        // Wait for all promises to resolve
        const foodResults = await Promise.all(foodPromises);

        // Filter out null results
        this.cartDetails = foodResults.filter(Boolean);
        console.log('Filtered cart details after API fetch:', this.cartDetails);

        // Update filterFoods for the template
        this.filterFoods = [...this.cartDetails];

        // Save the details to localStorage for future use
        if (this.cartDetails.length > 0) {
          localStorage.setItem('cartDetails', JSON.stringify(this.cartDetails));
          console.log('Saved fetched food details to localStorage');
        } else {
          console.warn('No cart details to save after API fetch');
        }

        console.log('Successfully fetched food details for cart items:', this.cartDetails.length);
      } catch (error) {
        console.error('Error in fetchFoodsForCart:', error);
        // Initialize empty if we couldn't fetch any details
        if (!this.cartDetails || this.cartDetails.length === 0) {
          this.cartDetails = [];
          this.filterFoods = [];
        }
      } finally {
        this.isLoading = false;
      }
    },

    loadUserAddresses() {
      // Load user addresses from API or localStorage
      try {
        // Mock implementation - in a real app, this would be an API call
        // Using apiService to fetch addresses
        if (this.user && this.user.user_id) {
          apiService.getUserAddresses(this.user.user_id)
            .then(addresses => {
              this.userAddresses = addresses || [];

              // If there's a default address, select it automatically
              const defaultAddress = this.userAddresses.find(addr => addr.isDefault);
              if (defaultAddress) {
                this.selectedAddressId = defaultAddress.id;
                this.selectedAddress = defaultAddress;
                this.checkoutObj.address = `${defaultAddress.street}, ${defaultAddress.city}${defaultAddress.note ? ` (Note: ${defaultAddress.note})` : ''}`;
              }
            })
            .catch(error => {
              console.error('Error loading user addresses:', error);
              this.userAddresses = [];
            });
        }
      } catch (error) {
        console.error('Error in loadUserAddresses:', error);
        this.userAddresses = [];
      }
    },

    // Handle address selection from dropdown
    handleAddressSelection() {
      if (this.selectedAddressId) {
        this.selectedAddress = this.userAddresses.find(addr => addr.id == this.selectedAddressId);
        if (this.selectedAddress) {
          this.checkoutObj.address = `${this.selectedAddress.street}, ${this.selectedAddress.city}${this.selectedAddress.note ? ` (Note: ${this.selectedAddress.note})` : ''}`;
        }
      } else {
        this.selectedAddress = null;
        this.checkoutObj.address = '';
      }
    },

    // Change address (show address selection again)
    changeAddress() {
      this.selectedAddressId = '';
      this.selectedAddress = null;
      this.checkoutObj.address = '';
    },

    // Validate address form
    validateAddressForm() {
      let isValid = true;

      // Reset errors
      this.addressErrors = {
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        city: ''
      };

      // Validate recipient name
      if (!this.newAddress.recipient_name || !this.newAddress.recipient_name.trim()) {
        this.addressErrors.recipient_name = 'Recipient name is required';
        isValid = false;
      }

      // Validate recipient phone
      if (!this.newAddress.recipient_phone || !this.newAddress.recipient_phone.trim()) {
        this.addressErrors.recipient_phone = 'Phone number is required';
        isValid = false;
      }

      // Validate street address
      if (!this.newAddress.address_line1 || !this.newAddress.address_line1.trim()) {
        this.addressErrors.address_line1 = 'Street address is required';
        isValid = false;
      }

      // Validate city
      if (!this.newAddress.city || !this.newAddress.city.trim()) {
        this.addressErrors.city = 'City is required';
        isValid = false;
      }

      return isValid;
    },

    // Save new address
    async saveNewAddress() {
      if (!this.validateAddressForm()) {
        return;
      }

      try {
        // Handle default address logic
        if (this.newAddress.is_default) {
          // Unset any existing default address
          this.userAddresses.forEach(address => {
            address.is_default = false;
          });
        }

        if (this.user && this.user.user_id) {
          // Try to add via API
          try {
            const addressData = { ...this.newAddress };
            const newAddress = await apiService.addUserAddress(this.user.user_id, addressData);

            // If API call successful, use returned address with ID
            if (newAddress && newAddress.id) {
              this.userAddresses.push(newAddress);
              this.selectedAddressId = newAddress.id;
              this.selectedAddress = newAddress;
            } else {
              // Fallback to local add
              const localAddress = { ...this.newAddress, id: Date.now() };
              this.userAddresses.push(localAddress);
              this.selectedAddressId = localAddress.id;
              this.selectedAddress = localAddress;
            }
          } catch (apiError) {
            console.log('Could not add address via API:', apiError);
            // Fallback to local add
            const localAddress = { ...this.newAddress, id: Date.now() };
            this.userAddresses.push(localAddress);
            this.selectedAddressId = localAddress.id;
            this.selectedAddress = localAddress;
          }
        } else {
          // No user ID, just add locally
          const localAddress = { ...this.newAddress, id: Date.now() };
          this.userAddresses.push(localAddress);
          this.selectedAddressId = localAddress.id;
          this.selectedAddress = localAddress;
        }

        // Update checkout address
        this.checkoutObj.address = `${this.selectedAddress.street}, ${this.selectedAddress.city}${this.selectedAddress.note ? ` (Note: ${this.selectedAddress.note})` : ''}`;

        // If no addresses were marked as default, mark the first one
        if (this.userAddresses.length === 1) {
          this.userAddresses[0].isDefault = true;
        }

        // Close the form
        this.cancelNewAddress();
      } catch (error) {
        console.error('Error saving address:', error);
        alert('Error saving address. Please try again.');
      }
    },

    // Cancel new address form
    cancelNewAddress() {
      this.showAddressForm = false;
      this.newAddress = {
        address_label: 'Home',
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        address_line2: '',
        district: '',
        city: '',
        postal_code: '',
        country: 'Vietnam',
        is_default: false
      };
      this.addressErrors = {
        recipient_name: '',
        recipient_phone: '',
        address_line1: '',
        city: ''
      };
    },

    resetCheckErr() {
      this.errorObj.phoneErr = [];
      this.errorObj.addressErr = [];
      this.errorObj.payErr = [];
      this.errorObj.numErr = [];
      this.errorObj.nameErr = [];
      this.errorObj.exDateErr = [];
      this.errorObj.cvvErr = [];
    },

    checkEmptyErr() {
      for (var typeErr in this.errorObj) {
        if (this.errorObj[typeErr].length != 0) {
          return false;
        }
      }
      return true;
    },

    inputUpcase(e) {
      e.target.value = e.target.value.toUpperCase();
    },

    checkForm() {
      this.resetCheckErr();

      // Phone validate
      if (!this.checkoutObj.phone) {
        this.errorObj.phoneErr.push('Entering phone number is required');
      } else {
        // Allow phone numbers starting with either '84' (country code) or '0' (local format)
        if (!this.checkoutObj.phone.startsWith('84') && !this.checkoutObj.phone.startsWith('0')) {
          this.errorObj.phoneErr.push('Phone number must start with 84 or 0');
        }

        // Check phone number length based on format
        // Country code format (84xxx): 11 digits
        // Local format (0xxx): 10 digits
        const validLength = this.checkoutObj.phone.startsWith('84') ? 11 : 10;

        // Validate phone number length
        if (this.checkoutObj.phone.length !== validLength) {
          this.errorObj.phoneErr.push(`Phone number must be exactly ${validLength} digits`);
        }

        // If user is adding a new address but hasn't filled the form
        if (this.showAddressForm) {
          this.validateAddressForm();
        }
      } // End of else block for phone validation

      // Card validate
      if (!this.checkoutObj.paymentMethod) {
        this.errorObj.payErr.push('Selecting payment method is required');
      } else if (this.checkoutObj.paymentMethod == "card") {
        if (!this.cardObj.number) {
          this.errorObj.numErr.push('Entering card number is required');
        } else {
          if (!this.cardObj.number.startsWith('4')) {
            this.errorObj.numErr.push('Visa card numbers must start with 4');
          }

          if (this.cardObj.number.length != 16) {
            this.errorObj.numErr.push('Visa card numbers must have exactly 16 digits');
          }

          if (!/[0-9]{16}/.test(this.cardObj.number)) {
            this.errorObj.numErr.push('Visa card numbers can only contain numbers');
          }
        }

        if (!this.cardObj.name) {
          this.errorObj.nameErr.push('Entering name is required');
        } else {
          if (!/^[A-Za-z]+$/.test(this.cardObj.name.replace(/\s/g, ""))) {
            this.errorObj.nameErr.push('A name can only contain letters');
          }
        }

        if (!this.cardObj.expiryDate) {
          this.errorObj.exDateErr.push('Entering expiry date is required');
        }

        if (!this.cardObj.cvv) {
          this.errorObj.cvvErr.push('Entering cvv code is required');
        } else {
          if (this.cardObj.cvv.length != 3) {
            this.errorObj.cvvErr.push('Cvv code must have exactly 3 digits');
          }

          if (!/[0-9]{3}/.test(this.cardObj.cvv)) {
            this.errorObj.cvvErr.push('Cvv code can only contain numbers');
          }
        }
      } else if (this.checkoutObj.paymentMethod == "cash") {
        this.cardObj.number = "";
        this.cardObj.name = "";
        this.cardObj.expiryDate = "";
        this.cardObj.cvv = "";

        this.errorObj.numErr = [];
        this.errorObj.nameErr = [];
        this.errorObj.exDateErr = [];
        this.errorObj.cvvErr = [];
      }
    },

    isPaid() {
      if (this.checkoutObj.paymentMethod == "cash") {
        return false;
      }
      else if (this.checkoutObj.paymentMethod == "card") {
        return true;
      }
      return false; // Default return value
    },

    // No longer needed as sendBillDetails is moved to ApiService
    sendBillDetails(billId, foodId, qty) {
      return apiService.sendBillDetails(billId, foodId, qty);
    },

    // Method to handle bill details error
    handleBillDetailsError(error) {
      console.error('Error creating bill detail:', error);
      throw error;
    },

    handleSubmit(e) {
      e.preventDefault();
      this.checkForm();

      if (!this.checkEmptyErr()) {
        console.log('Form validation failed');
        return;
      }

      try {
        console.log('Processing order submission...');

        // Debug user authentication state
        this.debugUserAuth();

        // Check if user is authenticated and has user_id
        if (!this.user) {
          console.error('No user data available');
          alert('Please log in to place an order.');
          this.$router.push('/login');
          return false;
        }

        if (!this.user.user_id) {
          console.error('User data missing user_id:', this.user);
          alert('Invalid user session. Please log in again.');
          this.$router.push('/login');
          return false;
        }

        // Get price values as numbers and handle potential NaN values
        const discount = parseFloat(this.calculateSummaryPrice()[1]) || 0;
        const delivery = parseFloat(this.calculateSummaryPrice()[2]) || 0;
        const total = parseFloat(this.calculateSummaryPrice()[3]) || 0;

        // Create order data object
        const orderData = {
          userId: parseInt(this.user.user_id),
          items: this.cartDetails,
          phone: this.checkoutObj.phone,
          address: this.checkoutObj.address,
          paymentMethod: this.checkoutObj.paymentMethod,
          discount: discount,
          delivery: delivery,
          total: total,
          isPaid: this.isPaid()
        };

        console.log('Submitting order with data:', orderData);

        // Use the ApiService to place the order
        apiService.placeOrder(orderData).then((orderResult) => {
          console.log('Order placed successfully:', orderResult);

          // Clear local cart data
          this.cartItem = [];
          this.itemQuantity = [];
          this.cartDetails = [];

          // Navigate to thank you page
          this.$router.push({
            path: "/thank",
            query: { order_id: orderResult.orderId }
          });
        }).catch((error) => {
          console.error('Error processing order:', error);
          alert('There was an error processing your order. Please try again.');
        });

        // Note: The rest of the function is handled in the Promise chain above
        return true;
      } catch (error) {
        console.error('Error in handleSubmit:', error);
        return false;
      }
    }
  }
};
</script>

<style scoped>
.md-checkout {
  padding: var(--md-sys-spacing-large) var(--md-sys-spacing-large);
}

.md-section-header {
  margin-bottom: var(--md-sys-spacing-large);
}

.md-section-header__title {
  font-family: var(--md-sys-typescale-headline-large-font);
  font-size: var(--md-sys-typescale-headline-large-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-small) 0;
}

.md-section-header__subtitle {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.md-checkout__container {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  box-shadow: var(--md-elevation-level1);
}

.md-checkout__form {
  display: flex;
  flex-direction: column;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-large);
}

.md-checkout__content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
}

.md-checkout__section {
  margin-bottom: var(--md-sys-spacing-large);
}

.md-checkout__section-title {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-medium) 0;
}

.md-summary-card {
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-medium);
}

.md-summary-card__row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--md-sys-spacing-small);
}

.md-summary-card__label {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
}

.md-summary-card__value {
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-summary-card__total {
  border-top: 1px solid var(--md-sys-color-outline-variant);
  padding-top: var(--md-sys-spacing-small);
  margin-top: var(--md-sys-spacing-small);
}

.md-summary-card__total .md-summary-card__label,
.md-summary-card__total .md-summary-card__value {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
}

.md-form-field {
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-form-field__label {
  display: block;
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-form-field__input {
  width: 100%;
  padding: var(--md-sys-spacing-medium);
  font-size: var(--md-sys-typescale-body-large-size);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-medium);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease;
}

.md-form-field__input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
}

.md-form-field__error {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-error);
  margin-top: var(--md-sys-spacing-small);
}

.md-form-field__helper {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  margin-top: var(--md-sys-spacing-small);
  font-style: italic;
}

.md-selected-address__note {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  font-style: italic;
  margin-top: var(--md-sys-spacing-small);
}

.md-radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-radio {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.md-radio input[type="radio"] {
  margin-right: var(--md-sys-spacing-small);
  accent-color: var(--md-sys-color-primary);
  width: 20px;
  height: 20px;
}

.md-radio label {
  display: flex;
  align-items: center;
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
}

.md-radio label i {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-checkout__card-details {
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-medium);
}

.md-form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--md-sys-spacing-medium);
}

.md-checkout__actions {
  display: flex;
  justify-content: space-between;
  margin-top: var(--md-sys-spacing-large);
}

.md-address-form-container {
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-medium);
}

.md-address-form__title {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-medium) 0;
}

.md-form-actions {
  display: flex;
  gap: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-medium);
}

.md-selected-address {
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-medium);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: var(--md-sys-spacing-medium);
}

.md-selected-address__content {
  flex: 1;
}

.md-selected-address__label {
  font-family: var(--md-sys-typescale-title-small-font);
  font-size: var(--md-sys-typescale-title-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-small) 0;
}

.md-selected-address__text {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0 0 var(--md-sys-spacing-small) 0;
}

.md-checkbox {
  display: flex;
  align-items: center;
  margin-top: var(--md-sys-spacing-medium);
}

.md-checkbox input[type="checkbox"] {
  margin-right: var(--md-sys-spacing-small);
  accent-color: var(--md-sys-color-primary);
}

.md-checkbox label {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
  cursor: pointer;
}

.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-corner-full);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: 500;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.md-button i {
  font-size: var(--md-sys-typescale-title-medium-size);
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-elevation-level1);
}

.md-button--primary:hover {
  background-color: var(--md-sys-color-primary-hover);
  box-shadow: var(--md-elevation-level2);
}

.md-button--primary:disabled {
  background-color: var(--md-sys-color-outline);
  color: var(--md-sys-color-on-surface-variant);
  box-shadow: none;
  cursor: not-allowed;
}

.md-button--outline {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button--outline:hover {
  background-color: var(--md-sys-color-surface-variant);
}

/* Cart Items Summary Styles */
.md-cart-items-summary {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-large);
  margin-bottom: var(--md-sys-spacing-medium);
  box-shadow: var(--md-elevation-level1);
}

.md-cart-items-summary__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-large);
}

.md-cart-items-summary__list {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-cart-item-summary {
  display: flex;
  justify-content: space-between;
  padding-bottom: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-cart-item-summary:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.md-cart-item-summary__info {
  display: flex;
  flex-direction: column;
}

.md-cart-item-summary__name {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-cart-item-summary__desc {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-cart-item-summary__qty {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
}

.md-cart-item-summary__price {
  font-family: var(--md-sys-typescale-title-medium-font);
  color: var(--md-sys-color-primary);
  align-self: flex-start;
}

/* Media queries for responsiveness */
@media (max-width: 768px) {
  .md-checkout {
    padding: var(--md-sys-spacing-medium) var(--md-sys-spacing-small);
  }

  .md-checkout__form {
    padding: var(--md-sys-spacing-medium);
  }

  .md-form-grid {
    grid-template-columns: 1fr;
  }

  .md-checkout__actions {
    flex-direction: column;
    gap: var(--md-sys-spacing-medium);
  }

  .md-button {
    width: 100%;
  }

  .md-cart-item-summary {
    flex-direction: column;
    gap: var(--md-sys-spacing-small);
  }

  .md-cart-item-summary__price {
    align-self: flex-end;
  }
}

/* Address Selection Styles */
.md-select {
  position: relative;
  width: 100%;
}

.md-select__input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 0.25rem;
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  appearance: none;
  cursor: pointer;
}

.md-select__icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--md-sys-color-on-surface-variant);
}

.md-address-form-container {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: 0.5rem;
}

.md-form-field__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.md-button--text {
  background: none;
  border: none;
  color: var(--md-sys-color-primary);
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
}

.md-button--text:hover {
  background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  border-radius: 4px;
}

.md-button--small {
  font-size: 0.875rem;
}

.md-form-field__helper {
  font-size: 0.75rem;
  color: var(--md-sys-color-on-surface-variant);
  margin-top: 0.25rem;
  margin-bottom: 0;
}

.md-existing-phone {
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 0.25rem;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
}

.md-existing-phone__value {
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-address-form__title {
  margin-top: 0;
  margin-bottom: 1.25rem;
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.md-checkbox {
  display: flex;
  align-items: center;
  margin: 1rem 0;
  cursor: pointer;
}

.md-checkbox input {
  margin-right: 0.5rem;
}

.md-selected-address {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.md-selected-address__content {
  flex: 1;
}

.md-selected-address__label {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-selected-address__text {
  margin: 0.25rem 0;
  color: var(--md-sys-color-on-surface-variant);
  font-size: 0.9rem;
}
</style>
