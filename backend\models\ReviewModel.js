// import database connection
import db from "../config/database.js";

// Get all reviews for a specific food
export const getReviewsByFoodId = (foodId, result) => {
    db.query(
        `SELECT r.*, u.user_name, u.user_name as user_image
         FROM reviews r
         JOIN user u ON r.user_id = u.user_id
         WHERE r.food_id = ?
         ORDER BY r.created_at DESC`,
        [foodId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Get review by user and food
export const getReviewByUserAndFood = (userId, foodId, result) => {
    db.query(
        "SELECT * FROM reviews WHERE user_id = ? AND food_id = ?",
        [userId, foodId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results[0]);
            }
        }
    );
};

// Add a new review
export const addReview = (data, result) => {
    db.query("INSERT INTO reviews SET ?", data, (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Update a review
export const updateReview = (data, reviewId, result) => {
    db.query(
        "UPDATE reviews SET rating = ?, comment = ?, updated_at = CURRENT_TIMESTAMP WHERE review_id = ?",
        [data.rating, data.comment, reviewId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Delete a review
export const deleteReview = (reviewId, result) => {
    db.query("DELETE FROM reviews WHERE review_id = ?", [reviewId], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Report a review
export const reportReview = (reviewId, reason, result) => {
    db.query(
        "UPDATE reviews SET reported = true, report_reason = ? WHERE review_id = ?",
        [reason, reviewId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Get all reported reviews (for admin)
export const getReportedReviews = (result) => {
    db.query(
        `SELECT r.*, u.user_name, f.food_name
         FROM reviews r
         JOIN user u ON r.user_id = u.user_id
         JOIN food f ON r.food_id = f.food_id
         WHERE r.reported = true
         ORDER BY r.updated_at DESC`,
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Get user's reviews
export const getUserReviews = (userId, result) => {
    db.query(
        `SELECT r.*, f.food_name, f.food_src
         FROM reviews r
         JOIN food f ON r.food_id = f.food_id
         WHERE r.user_id = ?
         ORDER BY r.created_at DESC`,
        [userId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Get review statistics for admin dashboard
export const getReviewStatistics = (result) => {
    const statsQuery = `
        SELECT
            COUNT(*) as total_reviews,
            AVG(rating) as average_rating,
            COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
            COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
            COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
            COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
            COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star,
            COUNT(CASE WHEN reported = 1 THEN 1 END) as reported_reviews,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_reviews,
            COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_reviews,
            COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_reviews
        FROM reviews
    `;

    db.query(statsQuery, (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results[0]);
        }
    });
};

// Get all reviews with pagination for admin
export const getAllReviewsWithPagination = (page, limit, sortBy, sortOrder, filterRating, filterReported, result) => {
    const offset = (page - 1) * limit;
    let whereClause = '';
    let queryParams = [];

    // Build WHERE clause based on filters
    const conditions = [];
    if (filterRating) {
        conditions.push('r.rating = ?');
        queryParams.push(filterRating);
    }
    if (filterReported !== undefined) {
        conditions.push('r.reported = ?');
        queryParams.push(filterReported === 'true' ? 1 : 0);
    }

    if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    // Add pagination parameters
    queryParams.push(limit, offset);

    const query = `
        SELECT
            r.*,
            u.user_name,
            f.food_name,
            f.food_src,
            rr.response as admin_response,
            rr.response_date,
            rr.admin_id as response_admin_id
        FROM reviews r
        JOIN user u ON r.user_id = u.user_id
        JOIN food f ON r.food_id = f.food_id
        LEFT JOIN review_responses rr ON r.review_id = rr.review_id
        ${whereClause}
        ORDER BY r.${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
    `;

    // Get total count for pagination
    const countQuery = `
        SELECT COUNT(*) as total
        FROM reviews r
        ${whereClause}
    `;

    db.query(countQuery, queryParams.slice(0, -2), (err, countResults) => {
        if (err) {
            console.log(err);
            result(err, null);
            return;
        }

        db.query(query, queryParams, (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, {
                    reviews: results,
                    total: countResults[0].total,
                    page: page,
                    limit: limit,
                    totalPages: Math.ceil(countResults[0].total / limit)
                });
            }
        });
    });
};

// Add admin response to review
export const addAdminResponse = (reviewId, response, adminId, result) => {
    db.query(
        "INSERT INTO review_responses (review_id, response, admin_id, response_date) VALUES (?, ?, ?, NOW())",
        [reviewId, response, adminId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Update review status (for admin moderation)
export const updateReviewStatus = (reviewId, action, adminNotes, result) => {
    let query;
    let params;

    switch (action) {
        case 'approve':
            query = "UPDATE reviews SET reported = 0, admin_notes = ? WHERE review_id = ?";
            params = [adminNotes, reviewId];
            break;
        case 'hide':
            query = "UPDATE reviews SET hidden = 1, admin_notes = ? WHERE review_id = ?";
            params = [adminNotes, reviewId];
            break;
        case 'delete':
            query = "DELETE FROM reviews WHERE review_id = ?";
            params = [reviewId];
            break;
        default:
            result(new Error('Invalid action'), null);
            return;
    }

    db.query(query, params, (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};