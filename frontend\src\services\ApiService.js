import axios from 'axios';

/**
 * Service for making API calls to the backend
 */
class ApiService {
  /**
   * Get all food items from the menu
   * @returns {Promise<Array>} Promise that resolves to an array of food items
   */
  async getFoods() {
    try {
      const response = await axios.get('/api/foods');
      return this.mapFoodData(response.data);
    } catch (error) {
      console.error('Error fetching foods from API:', error);
      throw error;
    }
  }

  /**
   * Get a single food item by ID
   * @param {number|string} id - The food item ID
   * @returns {Promise<Object>} Promise that resolves to a food item object
   */
  async getFoodById(id) {
    try {
      console.log('Fetching food by ID:', id);

      // First try to get from API
      const response = await axios.get(`/api/foods/${id}`);

      if (response.data) {
        console.log('Food data from API:', response.data);
        // Transform the data to ensure consistency
        const transformedFood = {
          ...response.data,
          food_id: response.data.food_id || id,
          food_name: response.data.food_name || 'Unnamed Product',
          food_desc: response.data.food_desc || response.data.food_description || 'No description available',
          food_price: response.data.food_price || '0',
          food_discount: response.data.food_discount || '0',
          food_star: response.data.food_star || '0',
          food_vote: response.data.food_vote || '0',
          food_image: response.data.food_image || response.data.food_src || this.getDefaultImageByCategory(response.data.food_category)
        };

        return transformedFood;
      }

      // If API doesn't return data, try to find in all foods
      console.log('No direct API response, searching in all foods...');
      const allFoods = await this.getFoods();
      const food = allFoods.find(f => f.food_id == id);

      if (food) {
        console.log('Found food in all foods:', food);
        return food;
      }

      console.error('Food not found with ID:', id);
      return null;
    } catch (error) {
      console.error(`Error fetching food by ID ${id}:`, error);

      // Fallback: try to find in all foods
      try {
        console.log('API error, trying fallback search...');
        const allFoods = await this.getFoods();
        const food = allFoods.find(f => f.food_id == id);

        if (food) {
          console.log('Found food in fallback search:', food);
          return food;
        }
      } catch (fallbackError) {
        console.error('Fallback search also failed:', fallbackError);
      }

      throw error;
    }
  }

  /**
   * Register a new user
   * @param {Object} userData - User data to register
   * @returns {Promise<Object>} Promise that resolves to the created user
   */
  async registerUser(userData) {
    try {
      const response = await axios.post('/api/users/', userData);
      return response.data;
    } catch (error) {
      console.error('Error registering user:', error);
      throw error;
    }
  }

  /**
   * Get user by email
   * @param {string} email - User email to look up
   * @returns {Promise<Object>} Promise that resolves to the user data if found
   */
  async getUserByEmail(email) {
    try {
      const response = await axios.get(`/api/users/${email}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user with email ${email}:`, error);
      throw error;
    }
  }

  /**
   * Login user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Promise that resolves to authenticated user data
   */
  async loginUser(email, password) {
    try {
      // First get the user by email
      const user = await this.getUserByEmail(email);

      // If no user is found or password doesn't match, throw error
      if (!user || user.user_password !== password) {
        throw new Error('Invalid email or password');
      }

      // Don't return the password to the client
      const userWithoutPassword = {...user};
      delete userWithoutPassword.user_password;

      return userWithoutPassword;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * Get complete user profile by ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Promise that resolves to the complete user profile data
   */
  async getUserProfile(userId) {
    try {
      const response = await axios.get(`/api/users/profile/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user profile for ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update user profile
   * @param {number} userId - User ID
   * @param {Object} profileData - Updated profile data
   * @returns {Promise<Object>} Promise that resolves to the updated user data
   */
  async updateUserProfile(userId, profileData) {
    try {
      const response = await axios.put(`/api/users/profile/${userId}`, profileData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user profile for ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user addresses
   * @param {number} userId - User ID
   * @returns {Promise<Array>} Promise that resolves to an array of user addresses
   */
  async getUserAddresses(userId) {
    try {
      const response = await axios.get(`/api/users/${userId}/addresses`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching addresses for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Add a new address for a user
   * @param {number} userId - User ID
   * @param {Object} addressData - Address data to add
   * @returns {Promise<Object>} Promise that resolves to the created address
   */
  async addUserAddress(userId, addressData) {
    try {
      const response = await axios.post(`/api/users/${userId}/addresses`, addressData);
      return response.data;
    } catch (error) {
      console.error(`Error adding address for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update a user address
   * @param {number} userId - User ID
   * @param {number} addressId - Address ID
   * @param {Object} addressData - Updated address data
   * @returns {Promise<Object>} Promise that resolves to the updated address
   */
  async updateUserAddress(userId, addressId, addressData) {
    try {
      const response = await axios.put(`/api/users/${userId}/addresses/${addressId}`, addressData);
      return response.data;
    } catch (error) {
      console.error(`Error updating address ${addressId} for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a user address
   * @param {number} userId - User ID
   * @param {number} addressId - Address ID
   * @returns {Promise<Object>} Promise that resolves to a success message
   */
  async deleteUserAddress(userId, addressId) {
    try {
      const response = await axios.delete(`/api/users/${userId}/addresses/${addressId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting address ${addressId} for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user order history
   * @param {number} userId - User ID
   * @returns {Promise<Array>} Promise that resolves to an array of user orders
   */
  async getUserOrders(userId) {
    try {
      const response = await axios.get(`/api/users/${userId}/orders`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching orders for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get all users (admin function)
   * @returns {Promise<Array>} Promise that resolves to an array of all users
   */
  async getAllUsers() {
    try {
      const response = await axios.get('/api/admin/users');
      return response.data;
    } catch (error) {
      console.error('Error fetching all users:', error);
      throw error;
    }
  }

  /**
   * Create a new user (admin function)
   * @param {Object} userData - User data to create
   * @returns {Promise<Object>} Promise that resolves to the created user
   */
  async createUser(userData) {
    try {
      const response = await axios.post('/api/admin/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update a user (admin function)
   * @param {number} userId - User ID to update
   * @param {Object} userData - Updated user data
   * @returns {Promise<Object>} Promise that resolves to the updated user
   */
  async updateUser(userId, userData) {
    try {
      const response = await axios.put(`/api/admin/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a user (admin function)
   * @param {number} userId - User ID to delete
   * @returns {Promise<Object>} Promise that resolves to a success message
   */
  async deleteUser(userId) {
    try {
      const response = await axios.delete(`/api/admin/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Toggle user active status (admin function)
   * @param {number} userId - User ID to toggle
   * @param {boolean} isActive - Whether to activate or deactivate the user
   * @returns {Promise<Object>} Promise that resolves to the updated user
   */
  async toggleUserStatus(userId, isActive) {
    try {
      const response = await axios.put(`/api/admin/users/${userId}/toggle-status`, { isActive });
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update user role (admin function)
   * @param {number} userId - User ID to update
   * @param {string} role - New role ('user', 'admin', etc.)
   * @returns {Promise<Object>} Promise that resolves to the updated user
   */
  async updateUserRole(userId, role) {
    try {
      const response = await axios.put(`/api/admin/users/${userId}/update-role`, { role });
      return response.data;
    } catch (error) {
      console.error(`Error updating role for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get current shipping orders for a user (orders that are in-progress)
   * @param {number} userId - User ID
   * @returns {Promise<Array>} Promise that resolves to an array of current shipping orders
   */
  async getCurrentShippingOrders(userId) {
    try {
      const response = await axios.get(`/api/users/${userId}/orders/current`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching current orders for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get details of a specific order
   * @param {number} userId - User ID
   * @param {number} orderId - Order ID
   * @returns {Promise<Object>} Promise that resolves to the order details
   */
  async getOrderDetails(userId, orderId) {
    try {
      const response = await axios.get(`/api/users/${userId}/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching order ${orderId} for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Place a new order
   * @param {Object} orderData - The order data including items, shipping details, and payment info
   * @returns {Promise<Object>} Promise that resolves to the created order
   */
  async placeOrder(orderData) {
    try {
      // 1. Get new bill ID
      const billIdResponse = await axios.get("/api/billstatus/new");
      let billId;

      if (!billIdResponse.data || billIdResponse.data === "") {
        billId = 1;
      } else {
        billId = parseInt(billIdResponse.data.bill_id) + 1;
      }

      console.log('Generated bill ID:', billId);

      // 2. Create bill details entries for each item
      const billDetailsPromises = [];
      for (let i = 0; i < orderData.items.length; i++) {
        const item = orderData.items[i];
        billDetailsPromises.push(
          this.sendBillDetails(billId, item.food_id, item.item_qty)
        );
      }

      // Wait for all bill details to be sent
      await Promise.all(billDetailsPromises);

      // 3. Format current timestamp as MySQL DATETIME
      const now = new Date();
      const year = now.getFullYear();
      const month = ("0" + (now.getMonth() + 1)).slice(-2);
      const day = ("0" + now.getDate()).slice(-2);
      const hour = ("0" + now.getHours()).slice(-2);
      const min = ("0" + now.getMinutes()).slice(-2);
      const sec = ("0" + now.getSeconds()).slice(-2);
      const currentTime = `${year}-${month}-${day} ${hour}:${min}:${sec}`;

      // 4. Create bill status object
      const billStatus = {
        bill_id: billId,
        user_id: parseInt(orderData.userId),
        bill_phone: orderData.phone,
        bill_address: orderData.address,
        bill_when: currentTime,
        bill_method: orderData.paymentMethod,
        bill_discount: parseFloat(orderData.discount) || 0,
        bill_delivery: parseFloat(orderData.delivery) || 0,
        bill_total: parseFloat(orderData.total) || 0,
        bill_paid: orderData.isPaid ? 1 : 0,
        bill_status: 1 // Initial status (1 = order placed)
      };

      console.log('Submitting order with data:', billStatus);

      // 5. Submit the bill status
      const response = await axios.post("/api/billstatus", billStatus);

      // 6. Clear the cart
      await axios.delete("/api/cartItem/" + orderData.userId);

      // 7. Return order data with ID
      return {
        orderId: billId,
        ...response.data
      };
    } catch (error) {
      console.error('Error processing order:', error);
      throw error;
    }
  }

  /**
   * Send bill details for a single item
   * @param {number} billId - The bill ID
   * @param {number} foodId - The food item ID
   * @param {number} qty - The quantity of the item
   * @returns {Promise<Object>} Promise that resolves to the created bill detail
   */
  async sendBillDetails(billId, foodId, qty) {
    try {
      const response = await axios.post("/api/billdetails", {
        bill_id: billId,
        food_id: foodId,
        item_qty: qty  // ✅ Fixed: Use correct column name
      });
      return response.data;
    } catch (error) {
      console.error('Error creating bill detail:', error);
      throw error;
    }
  }



  /**
   * Maps the backend food data structure to the frontend structure
   * @param {Array} foodsData - Raw food data from backend
   * @returns {Array} - Transformed food data for frontend
   */
  mapFoodData(foodsData) {
    return foodsData.map(food => {
      // Ensure all food items have the required properties
      return {
        ...food,
        food_id: food.food_id || 0,
        food_name: food.food_name || 'Unnamed Product',
        food_desc: food.food_desc || food.food_description || 'No description available',
        food_price: food.food_price || '0',
        food_discount: food.food_discount || '0',
        food_star: food.food_star || '0',
        food_vote: food.food_vote || '0',
        food_src: food.food_src || food.food_image || this.getDefaultImageByCategory(food.food_category)
      };
    });
  }

  // ========================
  // REVIEW METHODS
  // ========================

  /**
   * Get all reviews for a specific food item
   * @param {number|string} foodId - Food item ID
   * @returns {Promise<Array>} Promise that resolves to an array of reviews
   */
  async getFoodReviews(foodId) {
    try {
      const response = await axios.get(`/api/foods/${foodId}/reviews`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching reviews for food ID ${foodId}:`, error);
      throw error;
    }
  }

  /**
   * Get user's review for a specific food item
   * @param {number} userId - User ID
   * @param {number|string} foodId - Food item ID
   * @returns {Promise<Object|null>} Promise that resolves to the user's review or null
   */
  async getUserReviewForFood(userId, foodId) {
    try {
      const response = await axios.get(`/api/users/${userId}/foods/${foodId}/review`);
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return null; // No review found
      }
      console.error(`Error fetching user review for food ID ${foodId}:`, error);
      throw error;
    }
  }

  /**
   * Add a new review
   * @param {Object} reviewData - Review data (user_id, food_id, rating, comment)
   * @returns {Promise<Object>} Promise that resolves to the created review
   */
  async addReview(reviewData) {
    try {
      const response = await axios.post('/api/reviews', reviewData);
      return response.data;
    } catch (error) {
      console.error('Error adding review:', error);
      throw error;
    }
  }

  /**
   * Update an existing review
   * @param {number} reviewId - Review ID
   * @param {Object} reviewData - Updated review data (rating, comment)
   * @returns {Promise<Object>} Promise that resolves to the updated review
   */
  async updateReview(reviewId, reviewData) {
    try {
      const response = await axios.put(`/api/reviews/${reviewId}`, reviewData);
      return response.data;
    } catch (error) {
      console.error(`Error updating review ID ${reviewId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a review
   * @param {number} reviewId - Review ID
   * @returns {Promise<Object>} Promise that resolves to a success message
   */
  async deleteReview(reviewId) {
    try {
      const response = await axios.delete(`/api/reviews/${reviewId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting review ID ${reviewId}:`, error);
      throw error;
    }
  }

  /**
   * Report a review
   * @param {number} reviewId - Review ID
   * @param {string} reason - Report reason
   * @returns {Promise<Object>} Promise that resolves to a success message
   */
  async reportReview(reviewId, reason) {
    try {
      const response = await axios.post(`/api/reviews/${reviewId}/report`, { reason });
      return response.data;
    } catch (error) {
      console.error(`Error reporting review ID ${reviewId}:`, error);
      throw error;
    }
  }

  /**
   * Get all reviews by a user
   * @param {number} userId - User ID
   * @returns {Promise<Array>} Promise that resolves to an array of user's reviews
   */
  async getUserReviews(userId) {
    try {
      const response = await axios.get(`/api/users/${userId}/reviews`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching reviews for user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get a default image based on food category
   * @param {string} category - Food category
   * @returns {string} - Path to default image
   */
  getDefaultImageByCategory(category) {
    // Default images based on category using existing assets
    const categoryImages = {
      'main': 'taco-img.png',
      'appetizer': 'nachos-img.png',
      'dessert': 'dessert-img.png',
      'drink': 'coca-img.png',
      'taco': 'taco-img.png',
      'burrito': 'burrito-img.png',
      'nachos': 'nachos-img.png',
      'side': 'salad-img.png',
      'salad': 'salad-img.png'
    };

    return categoryImages[category] || 'nachos-img.png';
  }


}

// Create a singleton instance
const apiService = new ApiService();

export default apiService;
